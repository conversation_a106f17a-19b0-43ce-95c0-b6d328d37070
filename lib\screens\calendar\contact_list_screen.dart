import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:darvis_app/utils/design_tokens.dart';
import 'dart:io';

// Contact Model
class Contact {
  final String id;
  final String name;
  final String phone;
  final String location;
  final String socialPlatform;
  final String socialUsername;
  final String notes;
  final String? imagePath;
  final DateTime dateAdded;

  Contact({
    required this.id,
    required this.name,
    required this.phone,
    required this.location,
    required this.socialPlatform,
    required this.socialUsername,
    required this.notes,
    this.imagePath,
    required this.dateAdded,
  });
}

class ContactListScreen extends StatefulWidget {
  const ContactListScreen({super.key});

  @override
  State<ContactListScreen> createState() => _ContactListScreenState();
}

class _ContactListScreenState extends State<ContactListScreen> with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  List<Contact> _allContacts = [];
  List<Contact> _filteredContacts = [];
  bool _isLoading = false;
  String _sortBy = 'name'; // 'name', 'date', 'location'
  
  late AnimationController _refreshController;
  
  @override
  void initState() {
    super.initState();
    _refreshController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _loadDummyData();
    _searchController.addListener(_filterContacts);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  void _loadDummyData() {
    setState(() {
      _isLoading = true;
    });

    // Simulate loading delay
    Future.delayed(const Duration(milliseconds: 800), () {
      _allContacts = [
        Contact(
          id: '1',
          name: 'Sarah Johnson',
          phone: '+**********',
          location: 'Coffee Shop Downtown',
          socialPlatform: 'Instagram',
          socialUsername: 'sarah_j_photos',
          notes: 'Amazing photographer, loves travel',
          dateAdded: DateTime.now().subtract(const Duration(days: 2)),
        ),
        Contact(
          id: '2',
          name: 'Mike Chen',
          phone: '+1987654321',
          location: 'Tech Conference 2024',
          socialPlatform: 'LinkedIn',
          socialUsername: 'mike-chen-dev',
          notes: 'Senior Flutter Developer at Google',
          dateAdded: DateTime.now().subtract(const Duration(days: 5)),
        ),
        Contact(
          id: '3',
          name: 'Emma Rodriguez',
          phone: '+1122334455',
          location: 'University Library',
          socialPlatform: 'Twitter',
          socialUsername: 'emma_codes',
          notes: 'Computer Science student, AI enthusiast',
          dateAdded: DateTime.now().subtract(const Duration(days: 1)),
        ),
        Contact(
          id: '4',
          name: 'David Kim',
          phone: '+1555666777',
          location: 'Startup Meetup',
          socialPlatform: 'WhatsApp',
          socialUsername: 'david.kim.startup',
          notes: 'Entrepreneur, building fintech solutions',
          dateAdded: DateTime.now().subtract(const Duration(days: 7)),
        ),
        Contact(
          id: '5',
          name: 'Lisa Thompson',
          phone: '+1999888777',
          location: 'Art Gallery Opening',
          socialPlatform: 'Instagram',
          socialUsername: 'lisa_art_world',
          notes: 'Curator and art collector',
          dateAdded: DateTime.now().subtract(const Duration(days: 3)),
        ),
      ];
      
      _sortContacts();
      setState(() {
        _isLoading = false;
      });
    });
  }

  void _filterContacts() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredContacts = List.from(_allContacts);
      } else {
        _filteredContacts = _allContacts.where((contact) {
          return contact.name.toLowerCase().contains(query) ||
                 contact.location.toLowerCase().contains(query);
        }).toList();
      }
    });
  }

  void _sortContacts() {
    switch (_sortBy) {
      case 'name':
        _allContacts.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'date':
        _allContacts.sort((a, b) => b.dateAdded.compareTo(a.dateAdded));
        break;
      case 'location':
        _allContacts.sort((a, b) => a.location.compareTo(b.location));
        break;
    }
    _filteredContacts = List.from(_allContacts);
    _filterContacts();
  }

  Future<void> _refreshContacts() async {
    _refreshController.repeat();
    await Future.delayed(const Duration(seconds: 1));
    _loadDummyData();
    _refreshController.stop();
  }

  void _showContactDetails(Contact contact) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _ContactDetailsModal(contact: contact),
    );
  }

  void _showSortMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: DesignTokens.backgroundCard,
      builder: (context) => _SortMenuModal(
        currentSort: _sortBy,
        onSortChanged: (sortBy) {
          setState(() {
            _sortBy = sortBy;
          });
          _sortContacts();
          Navigator.pop(context);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        child: Column(
          children: [
            _Header(
              onBackTap: () => Navigator.of(context).pop(),
              onSortTap: _showSortMenu,
            ),
            _SearchBar(controller: _searchController),
            Expanded(
              child: _isLoading
                ? _LoadingState()
                : _filteredContacts.isEmpty
                  ? _EmptyState()
                  : _ContactList(
                      contacts: _filteredContacts,
                      onContactTap: _showContactDetails,
                      onRefresh: _refreshContacts,
                      refreshController: _refreshController,
                    ),
            ),
            const _BottomNavBar(selectedIndex: 1),
          ],
        ),
      ),
    );
  }
}

// Header Component
class _Header extends StatelessWidget {
  final VoidCallback onBackTap;
  final VoidCallback onSortTap;

  const _Header({
    required this.onBackTap,
    required this.onSortTap,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: false,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingMd,
          vertical: DesignTokens.spacingSm,
        ),
        child: Column(
          children: [
            const SizedBox(height: DesignTokens.spacingXl), // Increased top spacing for better separation
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: onBackTap,
                  child: const Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const _GradientTitle(text: 'Contact List'),
                GestureDetector(
                  onTap: onSortTap,
                  child: SvgPicture.asset(
                    'assets/icons/contact_list.svg',
                    width: 24,
                    height: 24,
                    colorFilter: const ColorFilter.mode(
                      Colors.white,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Gradient Title Component
class _GradientTitle extends StatelessWidget {
  final String text;

  const _GradientTitle({required this.text});

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      shaderCallback: (bounds) => DesignTokens.appNameHeaderGradient.createShader(bounds),
      child: Text(
        text,
        style: DesignTokens.appNameStyle.copyWith(
          color: Colors.white,
          fontSize: 35,
        ),
      ),
    );
  }
}

// Search Bar Component
class _SearchBar extends StatelessWidget {
  final TextEditingController controller;

  const _SearchBar({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingMd,
        vertical: DesignTokens.spacingSm,
      ),
      child: TextField(
        controller: controller,
        style: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
        decoration: InputDecoration(
          filled: true,
          fillColor: DesignTokens.formFieldBackground,
          hintText: 'Search contacts...',
          hintStyle: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textMuted),
          prefixIcon: const Icon(
            Icons.search,
            color: DesignTokens.textMuted,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
            borderSide: BorderSide.none,
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: DesignTokens.spacingMd,
            vertical: DesignTokens.spacingSm,
          ),
        ),
      ),
    );
  }
}

// Contact List Component
class _ContactList extends StatelessWidget {
  final List<Contact> contacts;
  final Function(Contact) onContactTap;
  final Future<void> Function() onRefresh;
  final AnimationController refreshController;

  const _ContactList({
    required this.contacts,
    required this.onContactTap,
    required this.onRefresh,
    required this.refreshController,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: onRefresh,
      backgroundColor: DesignTokens.backgroundCard,
      color: DesignTokens.primaryInteractiveBlue,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
        itemCount: contacts.length,
        itemBuilder: (context, index) {
          final contact = contacts[index];
          return _ContactEntry(
            contact: contact,
            onTap: () => onContactTap(contact),
          );
        },
      ),
    );
  }
}

// Contact Entry Component
class _ContactEntry extends StatelessWidget {
  final Contact contact;
  final VoidCallback onTap;

  const _ContactEntry({
    required this.contact,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: DesignTokens.spacingSm),
      child: Material(
        color: DesignTokens.backgroundCard,
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          child: Padding(
            padding: const EdgeInsets.all(DesignTokens.spacingMd),
            child: Row(
              children: [
                // Profile Thumbnail
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                    color: DesignTokens.formFieldBackground,
                  ),
                  child: contact.imagePath != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                        child: Image.file(
                          File(contact.imagePath!),
                          fit: BoxFit.cover,
                        ),
                      )
                    : Center(
                        child: SvgPicture.asset(
                          'assets/icons/empty_contact.svg',
                          width: 24,
                          height: 24,
                          colorFilter: const ColorFilter.mode(
                            DesignTokens.textMuted,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                ),

                const SizedBox(width: DesignTokens.spacingMd),

                // Contact Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        contact.name,
                        style: DesignTokens.onboardingBodyStyle.copyWith(
                          color: DesignTokens.textPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: DesignTokens.spacingXs),
                      Text(
                        contact.location,
                        style: DesignTokens.menuItemTextStyle.copyWith(
                          color: DesignTokens.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),

                // Expand Button
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: DesignTokens.primaryAccentBlue,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Center(
                    child: SvgPicture.asset(
                      'assets/icons/expand_icon.svg',
                      width: 16,
                      height: 16,
                      colorFilter: const ColorFilter.mode(
                        DesignTokens.iconPrimary,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Loading State Component
class _LoadingState extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Container(
          margin: const EdgeInsets.only(bottom: DesignTokens.spacingSm),
          child: Material(
            color: DesignTokens.backgroundCard,
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
            child: Padding(
              padding: const EdgeInsets.all(DesignTokens.spacingMd),
              child: Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: DesignTokens.formFieldBackground,
                      borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                    ),
                  ),
                  const SizedBox(width: DesignTokens.spacingMd),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: 16,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: DesignTokens.formFieldBackground,
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        const SizedBox(height: DesignTokens.spacingXs),
                        Container(
                          height: 12,
                          width: 120,
                          decoration: BoxDecoration(
                            color: DesignTokens.formFieldBackground,
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: DesignTokens.formFieldBackground,
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

// Empty State Component
class _EmptyState extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            'assets/icons/empty_contact.svg',
            width: 80,
            height: 80,
            colorFilter: const ColorFilter.mode(
              DesignTokens.textMuted,
              BlendMode.srcIn,
            ),
          ),
          const SizedBox(height: DesignTokens.spacingLg),
          Text(
            'No contacts saved yet',
            style: DesignTokens.onboardingBodyStyle.copyWith(
              color: DesignTokens.textPrimary,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: DesignTokens.spacingSm),
          Text(
            'Add your first contact to get started',
            style: DesignTokens.menuItemTextStyle.copyWith(
              color: DesignTokens.textSecondary,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: DesignTokens.spacingLg),
          GestureDetector(
            onTap: () {
              // Navigate to Add Contact screen
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: DesignTokens.spacingLg,
                vertical: DesignTokens.spacingSm,
              ),
              decoration: BoxDecoration(
                color: DesignTokens.primaryInteractiveBlue,
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              ),
              child: const Text(
                'Add Contact',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Contact Details Modal
class _ContactDetailsModal extends StatelessWidget {
  final Contact contact;

  const _ContactDetailsModal({required this.contact});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: DesignTokens.backgroundCard,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(DesignTokens.borderRadiusLg),
          topRight: Radius.circular(DesignTokens.borderRadiusLg),
        ),
      ),
      child: Column(
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(DesignTokens.spacingMd),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Contact Details',
                  style: DesignTokens.cardTitleStyle,
                ),
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ],
            ),
          ),

          const Divider(color: DesignTokens.borderInteractiveElement),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(DesignTokens.spacingMd),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile Image
                  Center(
                    child: Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                        color: DesignTokens.formFieldBackground,
                      ),
                      child: contact.imagePath != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                            child: Image.file(
                              File(contact.imagePath!),
                              fit: BoxFit.cover,
                            ),
                          )
                        : Center(
                            child: SvgPicture.asset(
                              'assets/icons/empty_contact.svg',
                              width: 40,
                              height: 40,
                              colorFilter: const ColorFilter.mode(
                                DesignTokens.textMuted,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                    ),
                  ),

                  const SizedBox(height: DesignTokens.spacingLg),

                  // Contact Information
                  _DetailField(label: 'Name', value: contact.name),
                  _DetailField(label: 'Phone', value: contact.phone),
                  _DetailField(label: 'Meeting Location', value: contact.location),
                  _DetailField(label: 'Social Media', value: '${contact.socialPlatform}: ${contact.socialUsername}'),
                  _DetailField(label: 'Notes', value: contact.notes),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Detail Field Component
class _DetailField extends StatelessWidget {
  final String label;
  final String value;

  const _DetailField({
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: DesignTokens.spacingMd),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: DesignTokens.formLabelStyle,
          ),
          const SizedBox(height: DesignTokens.spacingXs),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(DesignTokens.spacingMd),
            decoration: BoxDecoration(
              color: DesignTokens.formFieldBackground,
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
            ),
            child: Text(
              value,
              style: DesignTokens.bodyStyle.copyWith(
                color: DesignTokens.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Sort Menu Modal
class _SortMenuModal extends StatelessWidget {
  final String currentSort;
  final Function(String) onSortChanged;

  const _SortMenuModal({
    required this.currentSort,
    required this.onSortChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingMd),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'Sort Contacts',
            style: DesignTokens.cardTitleStyle,
          ),
          const SizedBox(height: DesignTokens.spacingMd),
          _SortOption(
            title: 'Name (A-Z)',
            value: 'name',
            currentSort: currentSort,
            onTap: () => onSortChanged('name'),
          ),
          _SortOption(
            title: 'Date Added (Newest)',
            value: 'date',
            currentSort: currentSort,
            onTap: () => onSortChanged('date'),
          ),
          _SortOption(
            title: 'Meeting Location',
            value: 'location',
            currentSort: currentSort,
            onTap: () => onSortChanged('location'),
          ),
        ],
      ),
    );
  }
}

// Sort Option Component
class _SortOption extends StatelessWidget {
  final String title;
  final String value;
  final String currentSort;
  final VoidCallback onTap;

  const _SortOption({
    required this.title,
    required this.value,
    required this.currentSort,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isSelected = currentSort == value;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingMd,
          vertical: DesignTokens.spacingSm,
        ),
        margin: const EdgeInsets.only(bottom: DesignTokens.spacingXs),
        decoration: BoxDecoration(
          color: isSelected
            ? DesignTokens.primaryAccentBlue
            : DesignTokens.formFieldBackground,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
        ),
        child: Text(
          title,
          style: DesignTokens.bodyStyle.copyWith(
            color: isSelected
              ? DesignTokens.iconPrimary
              : DesignTokens.textPrimary,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
      ),
    );
  }
}

// Bottom Navigation Bar
class _BottomNavBar extends StatelessWidget {
  final int selectedIndex;

  const _BottomNavBar({required this.selectedIndex});

  @override
  Widget build(BuildContext context) {
    return BottomAppBar(
      color: DesignTokens.backgroundApp,
      elevation: 0,
      child: SizedBox(
        height: 70,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildNavItem(index: 0, iconPath: 'assets/icons/home_chat.svg', label: 'Home'),
            _buildNavItem(index: 1, iconPath: 'assets/icons/calendar_navbar.svg', label: 'Calendar'),
            _buildCentralNavItem(index: 2, iconPath: 'assets/icons/darvisnavbar_icon.svg'),
            _buildNavItem(index: 3, iconPath: 'assets/icons/inbox_icon.svg', label: 'Inbox'),
            _buildNavItem(index: 4, iconPath: 'assets/icons/profile_icon.svg', label: 'Profile'),
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem({required int index, required String iconPath, required String label}) {
    final bool isActive = selectedIndex == index;
    final Color color = isActive ? DesignTokens.primaryInteractiveBlue : DesignTokens.navigationInactive;

    return Expanded(
      child: InkWell(
        onTap: () {
          // Handle navigation
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(iconPath, colorFilter: ColorFilter.mode(color, BlendMode.srcIn), height: 24),
            const SizedBox(height: DesignTokens.spacingXs),
            Text(label, style: DesignTokens.navBarLabelStyle.copyWith(color: color)),
          ],
        ),
      ),
    );
  }

  Widget _buildCentralNavItem({required int index, required String iconPath}) {
    final bool isActive = selectedIndex == index;
    return Expanded(
      child: InkWell(
        onTap: () {
          // Handle navigation
        },
        child: ShaderMask(
          shaderCallback: (bounds) {
            return (isActive ? DesignTokens.backgroundGlowBlueGradient : DesignTokens.appNameHeaderGradient)
                .createShader(bounds);
          },
          child: Container(
            height: 60,
            width: 60,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
            ),
            child: Center(
              child: SvgPicture.asset(iconPath, height: 32),
            ),
          ),
        ),
      ),
    );
  }
}
