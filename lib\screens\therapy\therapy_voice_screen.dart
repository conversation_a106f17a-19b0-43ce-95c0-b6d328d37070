import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:darvis_app/utils/design_tokens.dart';

// Reusable asset path constants
const String _kDarvisLogo = 'assets/images/darvis_main.PNG'; // Updated to new logo
const String _kChatIcon = 'assets/icons/chat_icon.svg';
const String _kMicActiveIcon = 'assets/icons/mic_active.svg';
const String _kMicInactiveIcon = 'assets/icons/mic_inactive.svg';
const String _kCloseIcon = 'assets/icons/close_icon.svg';

// Enum to manage the distinct visual states of the therapy voice screen.
enum TherapyVoiceState { tapToSpeak, listening, speaking }

/// TherapyVoiceScreen: A calming therapy voice interaction interface.
class TherapyVoiceScreen extends StatefulWidget {
  const TherapyVoiceScreen({super.key});

  @override
  State<TherapyVoiceScreen> createState() => _TherapyVoiceScreenState();
}

class _TherapyVoiceScreenState extends State<TherapyVoiceScreen>
    with TickerProviderStateMixin {
  // Manages the current state of the therapy voice interaction.
  TherapyVoiceState _currentState = TherapyVoiceState.tapToSpeak;

  // Animation controllers
  late final AnimationController _blurController;
  late final AnimationController _logoController;
  late final Animation<double> _blurAnimation;
  late final Animation<double> _logoScaleAnimation;

  @override
  void initState() {
    super.initState();
    
    // Setup for the continuous blur animation
    _blurController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );
    _blurAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _blurController, curve: Curves.easeInOut),
    );

    // Setup for logo scaling animation
    _logoController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );
    _logoScaleAnimation = Tween<double>(begin: 0.6, end: 0.8).animate(
      CurvedAnimation(parent: _logoController, curve: Curves.elasticOut),
    );
  }

  @override
  void dispose() {
    _blurController.dispose();
    _logoController.dispose();
    super.dispose();
  }

  // --- State Transition Logic for Testing ---

  void _handleMicTap() {
    setState(() {
      if (_currentState == TherapyVoiceState.tapToSpeak) {
        _currentState = TherapyVoiceState.listening;
        _blurController.repeat(reverse: true); // Start blur animation
      } else {
        // Allow tapping the mic again to stop and return to tap to speak
        _currentState = TherapyVoiceState.tapToSpeak;
        _blurController.stop();
        _logoController.reverse(); // Scale logo back down
      }
    });
  }

  void _handleScreenTap() {
    // Tapping the screen when listening transitions to speaking
    if (_currentState == TherapyVoiceState.listening) {
      setState(() {
        _currentState = TherapyVoiceState.speaking;
        _logoController.forward(); // Scale logo up
      });
    }
  }

  void _handleCloseTap() {
    // This would eventually call a BLoC event or Navigator.pop(context).
    print('Close button tapped');
  }

  void _handleChatTap() {
    // This would eventually call a BLoC event or navigate to the chat screen.
    print('Chat button tapped');
  }

  // --- Helper Getters for State-Dependent UI ---

  String get _statusText {
    switch (_currentState) {
      case TherapyVoiceState.tapToSpeak:
        return 'Safe space enabled';
      case TherapyVoiceState.listening:
        return 'Safe space enabled';
      case TherapyVoiceState.speaking:
        return 'Safe space enabled';
    }
  }

  Gradient get _backgroundGradient {
    switch (_currentState) {
      case TherapyVoiceState.tapToSpeak:
        return DesignTokens.therapyNeutralBlurGradient;
      case TherapyVoiceState.listening:
        return DesignTokens.therapyListeningBlurGradient;
      case TherapyVoiceState.speaking:
        return DesignTokens.therapySpeakingBlurGradient;
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final safeAreaTop = MediaQuery.of(context).padding.top;
    final safeAreaBottom = MediaQuery.of(context).padding.bottom;
    
    // Calculate the blur area to extend from 20% below header to 20% above bottom controls
    final headerHeight = 80.0 + safeAreaTop;
    final bottomControlsHeight = 120.0 + safeAreaBottom;
    final blurStartY = headerHeight + (screenHeight * 0.2);
    final blurEndY = screenHeight - bottomControlsHeight - (screenHeight * 0.2);
    final blurHeight = blurEndY - blurStartY;

    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      body: GestureDetector(
        onTap: _handleScreenTap,
        child: Stack(
          children: [
            // Layer 1 - Animated Background Blur Effect
            Positioned(
              top: blurStartY,
              left: 0,
              right: 0,
              height: blurHeight,
              child: _AnimatedBackgroundBlur(
                gradient: _backgroundGradient,
                isAnimating: _currentState != TherapyVoiceState.tapToSpeak,
                blurAnimation: _blurAnimation,
              ),
            ),

            // Main UI column
            Column(
              children: [
                SizedBox(height: safeAreaTop + 40), // Safe area + padding
                
                // Header Section
                const _TherapySessionTitle(text: 'Your Session'),
                
                const Spacer(),
                
                // Layer 2 - Darvis Logo (centered)
                AnimatedBuilder(
                  animation: _logoScaleAnimation,
                  builder: (context, child) {
                    final scale = _currentState == TherapyVoiceState.speaking
                        ? _logoScaleAnimation.value
                        : 0.6;
                    return Transform.scale(
                      scale: scale,
                      child: Image.asset(
                        _kDarvisLogo,
                        width: MediaQuery.of(context).size.width * 0.6,
                      ),
                    );
                  },
                ),
                
                const SizedBox(height: 20),
                
                const Spacer(),
                
                // Bottom Controls
                _TherapyActionButtons(
                  currentState: _currentState,
                  onMicTap: _handleMicTap,
                  onCloseTap: _handleCloseTap,
                  onChatTap: _handleChatTap,
                ),
                
                SizedBox(height: safeAreaBottom + 40), // Safe area + padding
              ],
            ),

            // Safe space status text (Positioned at the bottom)
            Positioned(
              bottom: MediaQuery.of(context).padding.bottom + 150, // adjust this offset as needed
              left: 0,
              right: 0,
              child: Text(
                _statusText,
                style: DesignTokens.therapyStatusTextStyle,
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// A specialized widget to render the therapy session title with gradient.
class _TherapySessionTitle extends StatelessWidget {
  final String text;
  const _TherapySessionTitle({required this.text});

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      blendMode: BlendMode.srcIn,
      shaderCallback: (bounds) =>
          DesignTokens.appNameHeaderGradient.createShader(
        Rect.fromLTWH(0, 0, bounds.width, bounds.height),
      ),
      child: Text(text, style: DesignTokens.therapySessionTitleStyle),
    );
  }
}

/// A widget that handles the animated background blur with organic movement.
class _AnimatedBackgroundBlur extends StatelessWidget {
  final Gradient gradient;
  final bool isAnimating;
  final Animation<double> blurAnimation;

  const _AnimatedBackgroundBlur({
    required this.gradient,
    required this.isAnimating,
    required this.blurAnimation,
  });

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: isAnimating ? blurAnimation : const AlwaysStoppedAnimation(1.0),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
        decoration: BoxDecoration(gradient: gradient),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 80.0, sigmaY: 80.0),
          child: Container(color: Colors.transparent),
        ),
      ),
    );
  }
}

/// A row containing the three bottom action buttons for therapy session.
class _TherapyActionButtons extends StatelessWidget {
  final TherapyVoiceState currentState;
  final VoidCallback onMicTap;
  final VoidCallback onChatTap;
  final VoidCallback onCloseTap;

  const _TherapyActionButtons({
    required this.currentState,
    required this.onMicTap,
    required this.onChatTap,
    required this.onCloseTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingXl),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _CircularIconButton(
            iconPath: _kChatIcon,
            onTap: onChatTap,
            backgroundColor: DesignTokens.formFieldBackground,
          ),
          _TherapyMicrophoneButton(
            isActive: currentState == TherapyVoiceState.listening,
            onTap: onMicTap,
          ),
          _CircularIconButton(
            iconPath: _kCloseIcon,
            onTap: onCloseTap,
            backgroundColor: DesignTokens.formFieldBackground,
            iconSize: 18,
          ),
        ],
      ),
    );
  }
}

/// The central microphone button for therapy sessions.
class _TherapyMicrophoneButton extends StatelessWidget {
  final bool isActive;
  final VoidCallback onTap;

  const _TherapyMicrophoneButton({required this.isActive, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: const BoxDecoration(
          color: DesignTokens.micButtonBorder,
          shape: BoxShape.circle,
        ),
        child: CircleAvatar(
          radius: 36,
          backgroundColor: DesignTokens.primaryInteractiveBlue,
          child: SvgPicture.asset(
            isActive ? _kMicActiveIcon : _kMicInactiveIcon,
            height: 32,
            width: 32,
          ),
        ),
      ),
    );
  }
}

/// A smaller, circular icon button used for the side actions.
class _CircularIconButton extends StatelessWidget {
  final String iconPath;
  final VoidCallback onTap;
  final Color backgroundColor;
  final double iconSize;

  const _CircularIconButton({
    required this.iconPath,
    required this.onTap,
    required this.backgroundColor,
    this.iconSize = 24,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: CircleAvatar(
        radius: 28,
        backgroundColor: backgroundColor,
        child: SvgPicture.asset(
          iconPath,
          height: iconSize,
          width: iconSize,
          colorFilter: const ColorFilter.mode(
              DesignTokens.iconSecondary, BlendMode.srcIn),
        ),
      ),
    );
  }
}
