import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:darvis_app/utils/design_tokens.dart';

// Reusable asset path constants
const String _kDarvisLogo = 'assets/images/darvis_main.PNG';
const String _kChatIcon = 'assets/icons/chat_icon.svg';
const String _kMicActiveIcon = 'assets/icons/mic_active.svg';
const String _kMicInactiveIcon = 'assets/icons/mic_inactive.svg';
const String _kCloseIcon = 'assets/icons/close_icon.svg';

// Enum to manage the distinct visual states of the screen.
enum VoiceState { idle, listening, speaking }

/// VoiceScreen: A full-screen voice interaction interface for Darvis.
class VoiceScreen extends StatefulWidget {
  const VoiceScreen({super.key});

  @override
  State<VoiceScreen> createState() => _VoiceScreenState();
}

class _VoiceScreenState extends State<VoiceScreen>
    with TickerProviderStateMixin {
  // Manages the current state of the voice interaction.
  VoiceState _currentState = VoiceState.idle;

  // Animation controller for the background glow pulse.
  late final AnimationController _pulseController;
  late final Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    // Setup for the continuous pulsing animation.
    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.15).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  // --- State Transition Logic for Testing ---

  void _handleMicTap() {
    setState(() {
      if (_currentState == VoiceState.idle) {
        _currentState = VoiceState.listening;
        _pulseController.repeat(reverse: true); // Start pulsing
      } else {
        // Allow tapping the mic again to stop listening/speaking and return to idle.
        _currentState = VoiceState.idle;
        _pulseController.stop();
      }
    });
  }

  void _handleScreenTap() {
    // As requested, tapping the screen when listening transitions to speaking.
    if (_currentState == VoiceState.listening) {
      setState(() {
        _currentState = VoiceState.speaking;
        // The pulse continues, but the color will change.
      });
    }
  }

  void _handleCloseTap() {
    // This would eventually call a BLoC event or Navigator.pop(context).
    print('Close button tapped');
  }

  void _handleChatTap() {
    // This would eventually call a BLoC event or navigate to the chat screen.
    print('Chat button tapped');
  }

  // --- Helper Getters for State-Dependent UI ---

  String get _statusText {
    switch (_currentState) {
      case VoiceState.idle:
        return 'Tap the microphone to speak';
      case VoiceState.listening:
        return 'Listening...';
      case VoiceState.speaking:
        return 'Speaking...';
    }
  }

  Gradient get _backgroundGradient {
    // The background is always a radial gradient, but the colors change.
    switch (_currentState) {
      case VoiceState.idle:
      case VoiceState.listening:
        return DesignTokens.backgroundGlowBlueGradient;
      case VoiceState.speaking:
        return DesignTokens.backgroundGlowGreenGradient;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      body: GestureDetector(
        onTap:
            _handleScreenTap, // Main tap handler for listening -> speaking transition
        child: Stack(
          alignment: Alignment.center,
          children: [
            Center(
              child: SizedBox(
                width: 350,
                height: 350,
                child: _AnimatedBackgroundGlow(
                  gradient: _backgroundGradient,
                  isPulsing: _currentState != VoiceState.idle,
                  pulseAnimation: _pulseAnimation,
                ),
              ),
            ),

            // Main UI column for the logo and text.
            Column(
              children: [
                const SizedBox(height: 80), // Top padding
                const _GradientTitle(text: 'Darvis'),
                const SizedBox(height: 20),
                // The status text that changes based on the current state.
                Text(_statusText, style: DesignTokens.voiceStatusTextStyle),
                const Spacer(),
                Image.asset(_kDarvisLogo,
                    width: MediaQuery.of(context).size.width * 0.6),
                const Spacer(),
                // Bottom action buttons are placed in their own row.
                _ActionButtons(
                  currentState: _currentState,
                  onMicTap: _handleMicTap,
                  onCloseTap: _handleCloseTap,
                  onChatTap: _handleChatTap,
                ),
                const SizedBox(height: 60), // Bottom padding
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// A specialized widget to render text with a gradient fill.
class _GradientTitle extends StatelessWidget {
  final String text;
  const _GradientTitle({required this.text});

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      blendMode: BlendMode.srcIn,
      shaderCallback: (bounds) =>
          DesignTokens.appNameHeaderGradient.createShader(
        Rect.fromLTWH(0, 0, bounds.width, bounds.height),
      ),
      child: Text(text, style: DesignTokens.voiceScreenTitleStyle),
    );
  }
}

/// A widget that handles the animated background glow.
class _AnimatedBackgroundGlow extends StatelessWidget {
  final Gradient gradient;
  final bool isPulsing;
  final Animation<double> pulseAnimation;

  const _AnimatedBackgroundGlow({
    required this.gradient,
    required this.isPulsing,
    required this.pulseAnimation,
  });

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: isPulsing ? pulseAnimation : const AlwaysStoppedAnimation(1.0),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 500), // Duration for color fade
        curve: Curves.easeInOut,
        decoration: BoxDecoration(gradient: gradient),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 100.0, sigmaY: 100.0),
          child: Container(color: Colors.transparent),
        ),
      ),
    );
  }
}

/// A row containing the three bottom action buttons.
class _ActionButtons extends StatelessWidget {
  final VoiceState currentState;
  final VoidCallback onMicTap;
  final VoidCallback onChatTap;
  final VoidCallback onCloseTap;

  const _ActionButtons({
    required this.currentState,
    required this.onMicTap,
    required this.onChatTap,
    required this.onCloseTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingXl),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _CircularIconButton(
            iconPath: _kChatIcon,
            onTap: onChatTap,
            backgroundColor: DesignTokens.formFieldBackground,
          ),
          _MicrophoneButton(
            isActive: currentState == VoiceState.listening,
            onTap: onMicTap,
          ),
          _CircularIconButton(
            iconPath: _kCloseIcon,
            onTap: onCloseTap,
            backgroundColor: DesignTokens.formFieldBackground,
            iconSize: 18, // Smaller size
          ),
        ],
      ),
    );
  }
}

/// The central, larger microphone button with its border.
class _MicrophoneButton extends StatelessWidget {
  final bool isActive;
  final VoidCallback onTap;

  const _MicrophoneButton({required this.isActive, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(4), // Space for the border
        decoration: const BoxDecoration(
          color: DesignTokens.micButtonBorder,
          shape: BoxShape.circle,
        ),
        child: CircleAvatar(
          radius: 36,
          backgroundColor: DesignTokens.primaryInteractiveBlue,
          child: SvgPicture.asset(
            isActive ? _kMicActiveIcon : _kMicInactiveIcon,
            height: 32,
            width: 32,
          ),
        ),
      ),
    );
  }
}

/// A smaller, circular icon button used for the side actions.
class _CircularIconButton extends StatelessWidget {
  final String iconPath;
  final VoidCallback onTap;
  final Color backgroundColor;
  final double iconSize; // Add this line

  const _CircularIconButton({
    required this.iconPath,
    required this.onTap,
    required this.backgroundColor,
    this.iconSize = 24, // Default size
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: CircleAvatar(
        radius: 28,
        backgroundColor: backgroundColor,
        child: SvgPicture.asset(
          iconPath,
          height: iconSize,
          width: iconSize,
          colorFilter: const ColorFilter.mode(
              DesignTokens.iconSecondary, BlendMode.srcIn),
        ),
      ),
    );
  }
}
