import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:get_it/get_it.dart';
import 'package:darvis_app/screens/onboarding/onboarding_screen.dart';
import 'package:darvis_app/screens/auth/signup_screen.dart';


import 'utils/design_tokens.dart';
import 'services/service_locator.dart';
import 'blocs/auth/auth_bloc.dart';
import 'blocs/auth/auth_event.dart';
import 'blocs/auth/auth_state.dart';
import 'screens/auth/login_screen.dart';
import 'screens/auth/welcome_screen.dart';
import 'screens/home/<USER>';
import 'screens/voice/voice_screen.dart';
import 'screens/chat/chat_screen.dart';
import 'screens/calendar/calendar_screen.dart';
import 'screens/calendar/add_event_screen.dart';
import 'screens/calendar/add_contact_screen.dart';
import 'screens/calendar/contact_list_screen.dart';
import 'screens/therapy/mind_garden_screen.dart';
import 'screens/therapy/therapy_voice_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // TODO: Re-enable Firebase when ready for backend integration
  // Initialize Firebase
  // await Firebase.initializeApp();

  // TODO: Re-enable service locator when ready for backend integration
  // Setup service locator
  // await setupServiceLocator();

  runApp(const DarvisApp());
}

class DarvisApp extends StatelessWidget {
  const DarvisApp({super.key});

  @override
  Widget build(BuildContext context) {
    // TODO: Re-enable BLoC providers when ready for backend integration
    return MaterialApp(
      title: 'Darvis AI Assistant',
      theme: DesignTokens.darkTheme,
      home: const TherapyVoiceScreen(),
      // TODO: Re-enable auth-based routing when ready for backend integration
      // home: BlocBuilder<AuthBloc, AuthState>(
      //   builder: (context, state) {
      //     if (state is AuthAuthenticated) {
      //       return const HomeScreen();
      //     } else if (state is AuthUnauthenticated) {
      //       return const LoginScreen();
      //     } else {
      //       return const Scaffold(
      //         body: Center(
      //           child: CircularProgressIndicator(),
      //         ),
      //       );
      //     }
      //   },
      // ),
      debugShowCheckedModeBanner: false,
    );
  }
}
