import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lottie/lottie.dart';
import 'package:darvis_app/utils/design_tokens.dart';
import 'dart:math';

class MindGardenScreen extends StatefulWidget {
  const MindGardenScreen({super.key});

  @override
  State<MindGardenScreen> createState() => _MindGardenScreenState();
}

class _MindGardenScreenState extends State<MindGardenScreen> 
    with TickerProviderStateMixin {
  
  late AnimationController _lottieController;
  late AnimationController _pulseController;
  late AnimationController _buttonController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _buttonAnimation;
  
  bool _isLoading = true;
  bool _isQuoteLoading = false;
  String _currentQuote = '';
  String _quoteAuthor = '';
  int _sessionStreak = 7;
  String _selectedMood = '';
  bool _moodCaptured = false;
  
  final List<String> _moodEmojis = ['😊', '😌', '🤔', '😔', '😴'];
  final List<String> _moodLabels = ['Great', 'Calm', 'Thoughtful', 'Low', 'Tired'];
  
  final List<Map<String, String>> _quotesBank = [
    {
      'quote': 'No throwaway your garri say your neighbour dey cook rice. Him gas fit finish',
      'author': 'African Proverb'
    },
    {
      'quote': 'The flower that blooms in adversity is the rarest and most beautiful of all',
      'author': 'Mulan'
    },
    {
      'quote': 'Growth begins at the end of your comfort zone',
      'author': 'Neale Donald Walsch'
    },
    {
      'quote': 'You are exactly where you need to be for your next step forward',
      'author': 'Unknown'
    },
    {
      'quote': 'Healing is not linear, and that\'s perfectly okay',
      'author': 'Unknown'
    },
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadDailyQuote();
    _preloadAnimation();
  }

  void _initializeAnimations() {
    _lottieController = AnimationController(
      duration: const Duration(seconds: 8), // 8-12 second meditative cycle
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _buttonAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _buttonController,
      curve: Curves.easeInOut,
    ));
  }

  void _preloadAnimation() async {
    // Simulate preloading
    await Future.delayed(const Duration(milliseconds: 500));
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
      _lottieController.repeat();
    }
  }

  void _loadDailyQuote() async {
    setState(() {
      _isQuoteLoading = true;
    });
    
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));
    
    if (mounted) {
      final random = Random();
      final selectedQuote = _quotesBank[random.nextInt(_quotesBank.length)];
      
      setState(() {
        _currentQuote = selectedQuote['quote']!;
        _quoteAuthor = selectedQuote['author']!;
        _isQuoteLoading = false;
      });
    }
  }

  void _onAnimationTap() {
    HapticFeedback.lightImpact();
    _lottieController.reset();
    _lottieController.repeat(); // keep looping after the tap
    _pulseController.forward().then((_) => _pulseController.reverse());
  }

  void _onButtonPress(VoidCallback onPressed) {
    HapticFeedback.mediumImpact();
    _buttonController.forward().then((_) {
      _buttonController.reverse();
      onPressed();
    });
  }

  void _selectMood(int index) {
    HapticFeedback.selectionClick();
    setState(() {
      _selectedMood = _moodEmojis[index];
      _moodCaptured = true;
    });

    // Show confirmation feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Mood captured: ${_moodLabels[index]} 😊',
          style: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
        ),
        backgroundColor: DesignTokens.primaryAccentBlue.withValues(alpha: 0.9),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
        ),
      ),
    );
  }

  void _refreshQuote() {
    HapticFeedback.lightImpact();
    _loadDailyQuote();
  }

  @override
  void dispose() {
    _lottieController.dispose();
    _pulseController.dispose();
    _buttonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      body: Stack(
        children: [
          // Background glow from login screen
          const _BackgroundGlow(),

          // Main scrollable content
          SafeArea(
            child: Column(
              children: [
                _Header(onBackTap: () => Navigator.of(context).pop()),

                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(
                      horizontal: DesignTokens.spacingLg,
                    ),
                    child: Column(
                      children: [
                        const SizedBox(height: DesignTokens.spacingXs),

                        // Lottie Animation Section
                        _AnimationSection(
                          isLoading: _isLoading,
                          lottieController: _lottieController,
                          pulseAnimation: _pulseAnimation,
                          onTap: _onAnimationTap,
                        ),

                        const SizedBox(height: DesignTokens.spacingSm),

                        // Daily Quote Section
                        _QuoteSection(
                          isLoading: _isQuoteLoading,
                          quote: _currentQuote,
                          author: _quoteAuthor,
                          onRefresh: _refreshQuote,
                        ),

                        const SizedBox(height: DesignTokens.spacingXl),

                        // Streak Counter
                        _StreakCounter(streak: _sessionStreak),

                        const SizedBox(height: DesignTokens.spacingLg),

                        // Mood Check-in
                        _MoodCheckIn(
                          moodEmojis: _moodEmojis,
                          moodLabels: _moodLabels,
                          selectedMood: _selectedMood,
                          moodCaptured: _moodCaptured,
                          onMoodSelect: _selectMood,
                        ),

                        const SizedBox(height: DesignTokens.spacingXl),

                        // Action Buttons
                        _ActionButtons(
                          buttonAnimation: _buttonAnimation,
                          onButtonPress: _onButtonPress,
                        ),

                        const SizedBox(height: DesignTokens.spacingXl * 2), // Extra space for bottom glow
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Header Component
class _Header extends StatelessWidget {
  final VoidCallback onBackTap;

  const _Header({required this.onBackTap});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingLg,
        vertical: DesignTokens.spacingLg,
      ),
      child: Column(
        children: [
          const SizedBox(height: DesignTokens.spacingMd),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: onBackTap,
                child: Container(
                  padding: const EdgeInsets.all(DesignTokens.spacingSm),
                  decoration: BoxDecoration(
                    color: DesignTokens.backgroundCard.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                  ),
                  child: const Icon(
                    Icons.arrow_back,
                    color: DesignTokens.iconPrimary,
                    size: 24,
                  ),
                ),
              ),
              ShaderMask(
                shaderCallback: (bounds) => DesignTokens.appNameHeaderGradient.createShader(bounds),
                child: Text(
                  'Mind Garden',
                  style: DesignTokens.appNameStyle.copyWith(
                    color: Colors.white,
                    fontSize: 38,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
              const SizedBox(width: 48), // Balance the back button
            ],
          ),
        ],
      ),
    );
  }
}

// Animation Section Component
class _AnimationSection extends StatelessWidget {
  final bool isLoading;
  final AnimationController lottieController;
  final Animation<double> pulseAnimation;
  final VoidCallback onTap;

  const _AnimationSection({
    required this.isLoading,
    required this.lottieController,
    required this.pulseAnimation,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedBuilder(
        animation: pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: pulseAnimation.value,
            child: Container(
              width: 200,
              height: 200,
              child: Center(
                child: isLoading
                  ? Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: DesignTokens.formFieldBackground.withValues(alpha: 0.3),
                        shape: BoxShape.circle,
                      ),
                      child: const Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            DesignTokens.primaryInteractiveBlue,
                          ),
                        ),
                      ),
                    )
                  : Lottie.asset(
                      'assets/animations/therapy_mode.json',
                      controller: lottieController,
                      width: 160,
                      height: 160,
                      fit: BoxFit.contain,
                      repeat: true,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 120,
                          height: 120,
                          decoration: const BoxDecoration(
                            color: DesignTokens.primaryAccentBlue,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.local_florist,
                            color: DesignTokens.iconPrimary,
                            size: 60,
                          ),
                        );
                      },
                    ),
              ),
            ),
          );
        },
      ),
    );
  }
}

// Quote Section Component
class _QuoteSection extends StatelessWidget {
  final bool isLoading;
  final String quote;
  final String author;
  final VoidCallback onRefresh;

  const _QuoteSection({
    required this.isLoading,
    required this.quote,
    required this.author,
    required this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          'A Thought for You',
          style: DesignTokens.onboardingBodyStyle.copyWith(
            color: DesignTokens.textSecondary,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),

        const SizedBox(height: DesignTokens.spacingMd),

        Container(
          padding: const EdgeInsets.all(DesignTokens.spacingLg),
          decoration: BoxDecoration(
            color: DesignTokens.backgroundCard.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
            border: Border.all(
              color: DesignTokens.primaryAccentBlue.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: isLoading
            ? Column(
                children: [
                  Container(
                    height: 20,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: DesignTokens.formFieldBackground.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  const SizedBox(height: DesignTokens.spacingSm),
                  Container(
                    height: 20,
                    width: 200,
                    decoration: BoxDecoration(
                      color: DesignTokens.formFieldBackground.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ],
              )
            : Column(
                children: [
                  Text(
                    quote,
                    style: DesignTokens.onboardingBodyStyle.copyWith(
                      color: DesignTokens.textPrimary,
                      fontSize: 18,
                      fontWeight: FontWeight.w400,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: DesignTokens.spacingMd),

                  Text(
                    '— $author',
                    style: DesignTokens.menuItemTextStyle.copyWith(
                      color: DesignTokens.textSecondary,
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
        ),

        const SizedBox(height: DesignTokens.spacingSm),

        GestureDetector(
          onTap: onRefresh,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.refresh,
                color: DesignTokens.textMuted,
                size: 16,
              ),
              const SizedBox(width: DesignTokens.spacingXs),
              Text(
                'Today\'s reflection • Tap to refresh',
                style: DesignTokens.menuItemTextStyle.copyWith(
                  color: DesignTokens.textMuted,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

// Streak Counter Component
class _StreakCounter extends StatelessWidget {
  final int streak;

  const _StreakCounter({required this.streak});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingLg,
        vertical: DesignTokens.spacingSm,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            DesignTokens.primaryInteractiveBlue.withValues(alpha: 0.2),
            DesignTokens.primaryAccentBlue.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.local_fire_department,
            color: DesignTokens.primaryInteractiveBlue,
            size: 20,
          ),
          const SizedBox(width: DesignTokens.spacingSm),
          Text(
            '$streak days of growth',
            style: DesignTokens.onboardingBodyStyle.copyWith(
              color: DesignTokens.textPrimary,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

// Mood Check-in Component
class _MoodCheckIn extends StatelessWidget {
  final List<String> moodEmojis;
  final List<String> moodLabels;
  final String selectedMood;
  final bool moodCaptured;
  final Function(int) onMoodSelect;

  const _MoodCheckIn({
    required this.moodEmojis,
    required this.moodLabels,
    required this.selectedMood,
    required this.moodCaptured,
    required this.onMoodSelect,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          'How are you feeling today?',
          style: DesignTokens.onboardingBodyStyle.copyWith(
            color: DesignTokens.textSecondary,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),

        const SizedBox(height: DesignTokens.spacingMd),

        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(moodEmojis.length, (index) {
            final isSelected = selectedMood == moodEmojis[index];

            return GestureDetector(
              onTap: () => onMoodSelect(index),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: const EdgeInsets.all(DesignTokens.spacingSm),
                decoration: BoxDecoration(
                  color: isSelected
                    ? DesignTokens.primaryAccentBlue.withValues(alpha: 0.3)
                    : DesignTokens.backgroundCard.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                  border: Border.all(
                    color: isSelected
                      ? DesignTokens.primaryInteractiveBlue
                      : Colors.transparent,
                    width: 2,
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      moodEmojis[index],
                      style: const TextStyle(fontSize: 24),
                    ),
                    const SizedBox(height: DesignTokens.spacingXs),
                    Text(
                      moodLabels[index],
                      style: DesignTokens.menuItemTextStyle.copyWith(
                        color: isSelected
                          ? DesignTokens.textPrimary
                          : DesignTokens.textMuted,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }),
        ),

        const SizedBox(height: DesignTokens.spacingMd),

        Text(
          moodCaptured
            ? 'Mood captured! You\'re growing beautifully ✨'
            : 'You\'re growing beautifully',
          style: DesignTokens.menuItemTextStyle.copyWith(
            color: moodCaptured
              ? DesignTokens.primaryAccentBlue
              : DesignTokens.primaryInteractiveBlue,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}

// Action Buttons Component
class _ActionButtons extends StatelessWidget {
  final Animation<double> buttonAnimation;
  final Function(VoidCallback) onButtonPress;

  const _ActionButtons({
    required this.buttonAnimation,
    required this.onButtonPress,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: buttonAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: buttonAnimation.value,
          child: Column(
            children: [
              // Begin Session Button
              GestureDetector(
                onTap: () => onButtonPress(() {
                  // Navigate to therapy session
                }),
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(
                    vertical: DesignTokens.spacingMd,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        DesignTokens.primaryInteractiveBlue,
                        DesignTokens.primaryAccentBlue,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                    boxShadow: [
                      BoxShadow(
                        color: DesignTokens.primaryInteractiveBlue.withValues(alpha: 0.3),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Text(
                    'Begin Session',
                    style: DesignTokens.onboardingButtonStyle.copyWith(
                      color: DesignTokens.textPrimary,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),

              const SizedBox(height: DesignTokens.spacingMd),

              // My Progress Button
              GestureDetector(
                onTap: () => onButtonPress(() {
                  // Navigate to progress screen
                }),
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(
                    vertical: DesignTokens.spacingMd,
                  ),
                  decoration: BoxDecoration(
                    color: DesignTokens.backgroundCard.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                    border: Border.all(
                      color: DesignTokens.primaryAccentBlue.withValues(alpha: 0.5),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    'My Progress',
                    style: DesignTokens.onboardingBodyStyle.copyWith(
                      color: DesignTokens.textPrimary,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

// Background Glow Component (from login screen)
class _BackgroundGlow extends StatelessWidget {
  const _BackgroundGlow();

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.1,
        decoration: const BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Color.fromARGB(255, 9, 38, 107),
              blurRadius: 150.0,
              spreadRadius: 50.0,
            ),
          ],
        ),
      ),
    );
  }
}
