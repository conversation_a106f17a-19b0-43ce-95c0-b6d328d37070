import 'package:flutter/material.dart';

/// Design tokens for the Darvis AI Assistant app
/// Auto-generated from design_tokens.yaml - DO NOT EDIT MANUALLY
class DesignTokens {
  // Colors
  static const Color primaryDeepBlue = Color(0xFF111D2F);
  static const Color primaryAccentBlue = Color(0xFF183C8E);
  static const Color primaryInteractiveBlue = Color(0xFF004EEB);

  static const Color backgroundApp = Color(0xFF000000);
  static const Color backgroundCard = Color(0xFF111D2F);
  static const Color backgroundAccentCard = Color(0xFF183C8E);
  static const Color backgroundChatBubbleUser = Color(0xFF0A1B3F);
  static const Color backgroundChatBubbleBot = Color(0xFF171717);
  static const Color backgroundTextInput = Color(0xFFDCDFE7);
  static const Color backgroundSideMenu = Color(0xFF020D1E);

  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFFA9B4C8);
  static const Color textMuted = Color(0xFF7B8499);
  static const Color textOnLightBackground = Color(0xFF222425);

  static const Color iconPrimary = Color(0xFFFFFFFF);
  static const Color iconSecondary = Color(0xFFA9B4C8);

  static const Color borderInteractiveElement = Color(0xFF262626);

  static const Color navigationInactive = Color(0xFF676D75);

  // Spacing
  static const double spacingXs = 4.0;
  static const double spacingSm = 8.0;
  static const double spacingMd = 16.0;
  static const double spacingLg = 24.0;
  static const double spacingXl = 32.0;
  static const double spacingXxl = 48.0;

  // Border Radius
  static const double borderRadiusSm = 8.0;
  static const double borderRadiusMd = 16.0;
  static const double borderRadiusLg = 20.0;
  static const double borderRadiusXl = 24.0;
  static const double borderRadiusChatBubble = 18.0;

  // Typography
  static const String fontFamilyHeading = 'Outfit';
  static const String fontFamilyBody = 'Poppins';

  // Text Styles
  static const TextStyle appNameStyle = TextStyle(
    fontFamily: fontFamilyHeading,
    fontSize: 36,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle cardTitleStyle = TextStyle(
    fontFamily: fontFamilyHeading,
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: textPrimary,
  );

  static const TextStyle bodyStyle = TextStyle(
    fontFamily: fontFamilyBody,
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: textSecondary,
  );

  static const TextStyle chatMessageStyle = TextStyle(
    fontFamily: fontFamilyBody,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: textPrimary,
  );

  static const TextStyle onboardingHeadlineStyle = TextStyle(
    fontFamily: fontFamilyHeading,
    fontSize: 32,
    fontWeight: FontWeight.w700,
    color: textPrimary,
  );

  static const TextStyle onboardingBodyStyle = TextStyle(
    fontFamily: fontFamilyBody,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: textSecondary,
  );

  static const TextStyle onboardingButtonStyle = TextStyle(
    fontFamily: fontFamilyBody,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: textPrimary,
  );

  static const TextStyle onboardingButtonBoldStyle = TextStyle(
    fontFamily: fontFamilyBody,
    fontSize: 16,
    fontWeight: FontWeight.w700,
    color: textPrimary,
  );

  static const TextStyle formLabelStyle = TextStyle(
    fontFamily: fontFamilyHeading,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: textPrimary,
  );

  static const TextStyle googleButtonTextStyle = TextStyle(
    fontFamily: fontFamilyHeading,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: textPrimary,
  );

  static const TextStyle primaryButtonTextStyle = TextStyle(
    fontFamily: fontFamilyBody,
    fontSize: 16,
    fontWeight: FontWeight.w700,
    color: textOnLightBackground,
  );

  static const TextStyle linkTextStyle = TextStyle(
    fontFamily: fontFamilyBody,
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: primaryInteractiveBlue,
  );

  static const TextStyle welcomeHeadlineStyle = TextStyle(
    fontFamily: fontFamilyHeading,
    fontSize: 36,
    fontWeight: FontWeight.w600,
    color: textPrimary,
  );

  static const TextStyle greetingTextStyle = TextStyle(
    fontFamily: fontFamilyHeading,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: textPrimary,
  );

  static const TextStyle cardLargeHeadingStyle = TextStyle(
    fontFamily: fontFamilyHeading,
    fontSize: 20,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle cardSubheadingStyle = TextStyle(
    fontFamily: fontFamilyHeading,
    fontSize: 14,
    fontWeight: FontWeight.w400,
  );

  static const TextStyle cardListItemStyle = TextStyle(
    fontFamily: fontFamilyBody,
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: textPrimary,
  );

  static const TextStyle navBarLabelStyle = TextStyle(
    fontFamily: fontFamilyHeading,
    fontSize: 11,
    fontWeight: FontWeight.w400,
  );

  static const TextStyle voiceScreenTitleStyle = TextStyle(
    fontFamily: fontFamilyHeading,
    fontSize: 70,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle voiceStatusTextStyle = TextStyle(
    fontFamily: fontFamilyHeading,
    fontSize: 15,
    fontWeight: FontWeight.w400,
  );

  static const TextStyle menuSectionTitleStyle = TextStyle(
    fontFamily: fontFamilyHeading,
    fontSize: 24,
    fontWeight: FontWeight.w400,
    color: textPrimary,
  );

  static const TextStyle menuItemTextStyle = TextStyle(
    fontFamily: fontFamilyBody,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: textPrimary,
  );

  static const TextStyle textInputPlaceholderStyle = TextStyle(
    fontFamily: fontFamilyHeading,
    fontSize: 10,
    fontWeight: FontWeight.w400,
    color: textOnLightBackground,
  );

  static const TextStyle calendarDropdownItemStyle = TextStyle(
    fontFamily: fontFamilyBody,
    fontSize: 10,
    fontWeight: FontWeight.w400,
  );

  static const TextStyle calendarMonthNameStyle = TextStyle(
    fontFamily: fontFamilyHeading,
    fontSize: 16,
    fontWeight: FontWeight.w700,
  );

  static const TextStyle calendarDayOfWeekStyle = TextStyle(
    fontFamily: fontFamilyHeading,
    fontSize: 17,
    fontWeight: FontWeight.w400,
  );

  static const TextStyle calendarDateNumberStyle = TextStyle(
    fontFamily: fontFamilyHeading,
    fontSize: 16,
    fontWeight: FontWeight.w400,
  );

  static const TextStyle eventListItemTitleStyle = TextStyle(
    fontFamily: fontFamilyHeading,
    fontSize: 13,
    fontWeight: FontWeight.w400,
  );

  static const TextStyle eventListItemTimeStyle = TextStyle(
    fontFamily: fontFamilyBody,
    fontSize: 13,
    fontWeight: FontWeight.w400,
  );

  // Gradients
  static const LinearGradient appNameHeaderGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [Color(0xFFDCDFE7), Color(0xFF54565D)],
  );

  static const RadialGradient backgroundGlowBlueGradient = RadialGradient(
    center: Alignment.center,
    colors: [Color(0xFF183C8E), Color(0xFF11272F)],
    stops: [0.0, 0.8],
  );

  static const RadialGradient backgroundGlowGreenGradient = RadialGradient(
    center: Alignment.center,
    colors: [Color(0xFF183C8E), Color(0xFF03411D)],
    stops: [0.0, 0.8],
  );

  static const LinearGradient todayCardBackgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [Color(0xFF111D2F), Color(0xFF183CBE)],
  );

  // Effects
  static const BoxShadow activeMicGlow = BoxShadow(
    color: Color.fromRGBO(24, 60, 142, 0.6),
    blurRadius: 15,
    spreadRadius: 5,
  );

  // Component Colors
  // Onboarding Indicator
  static const Color onboardingIndicatorActive = textPrimary;
  static const Color onboardingIndicatorInactive = Color(0xFF333333);

  // Component Colors
  static const Color googleButtonBackground = Color(0xFF262626);
  static const Color formFieldBackground = Color(0xFF262626);
  static const Color primaryButtonBackground = Color(0xFFDCDFE7);
  static const Color link = Color(0xFF014FFF);
  static const Color micButtonBorder = Color(0xFF00152D);
  static const Color modeToggleInactiveBackground = Color(0xFF283140);
  static const Color modeToggleActiveBackground = Color(0xFFDCDFE7);
  static const Color eventListItemBackground = Color(0xFF283140);
  static const Color calendarDropdownBackground = Color(0xFF283140);
  static const Color calendarTodayMarkerBackground = Color(0xFFDCDFE7);
  static const Color calendarSelectedMarkerOutline = Color(0xFF183CBE);

  // Theme
  static ThemeData get darkTheme {
    return ThemeData(
      brightness: Brightness.dark,
      primaryColor: primaryInteractiveBlue,
      scaffoldBackgroundColor: backgroundApp,
      fontFamily: fontFamilyBody,
      textTheme: const TextTheme(
        displayLarge: appNameStyle,
        headlineSmall: cardTitleStyle,
        bodyMedium: bodyStyle,
        bodyLarge: chatMessageStyle,
      ),
      colorScheme: const ColorScheme.dark(
        primary: primaryInteractiveBlue,
        secondary: primaryAccentBlue,
        surface: backgroundCard,
        onPrimary: textPrimary,
        onSecondary: textPrimary,
        onSurface: textPrimary,
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: backgroundTextInput,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusXl),
          borderSide: BorderSide.none,
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: spacingMd,
          vertical: spacingMd,
        ),
      ),
      cardTheme: CardThemeData(
        color: backgroundCard,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusLg),
        ),
      ),
    );
  }
}
