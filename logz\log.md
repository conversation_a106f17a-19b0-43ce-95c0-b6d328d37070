Launching lib\main.dart on 21081111RG in debug mode...
√ Built build\app\outputs\flutter-apk\app-debug.apk
I/flutter (14244): [IMPORTANT:flutter/shell/platform/android/android_context_vk_impeller.cc(61)] Using the Impeller rendering backend (Vulkan).
W/FlutterWebRTCPlugin(14244): audioFocusChangeListener [Earpiece(name=Earpiece)] Earpiece(name=Earpiece)
W/FlutterWebRTCPlugin(14244): audioFocusChangeListener [Speakerphone(name=Speakerphone), Earpiece(name=Earpiece)] Speakerphone(name=Speakerphone)
D/ProfileInstaller(14244): Installing profile for com.example.darvis_app
Connecting to VM Service at ws://127.0.0.1:4664/or9asnoeUqQ=/ws
Connected to the VM Service.
W/FinalizerDaemon(14244): type=1400 audit(0.0:245345): avc:  denied  { getopt } for  path="/dev/socket/usap_pool_primary" scontext=u:r:untrusted_app:s0:c173,c256,c512,c768 tcontext=u:r:zygote:s0 tclass=unix_stream_socket permissive=0 app=com.example.darvis_app
I/Choreographer(14244): Skipped 107 frames!  The application may be doing too much work on its main thread.
W/Looper  (14244): PerfMonitor doFrame : time=1ms vsyncFrame=0 latency=893ms procState=-1 historyMsgCount=1
W/libc    (14244): Access denied finding property "persist.vendor.debug.gpud.enable"
E/LB      (14244): fail to open file: No such file or directory
E/LB      (14244): fail to open node: No such file or directory
W/libc    (14244): Access denied finding property "ro.vendor.display.iris_x7.support"
D/BLASTBufferQueue(14244): [SurfaceView[com.example.darvis_app/com.example.darvis_app.MainActivity]#1](f:0,a:1) acquireNextBufferLocked size=1080x2270 mFrameNumber=1 applyTransaction=true mTimestamp=431431945088637(auto) mPendingTransactions.size=0 graphicBufferId=61177514164238 transform=0
I/Choreographer(14244): Skipped 56 frames!  The application may be doing too much work on its main thread.
D/VRI[MainActivity](14244): vri.Setup new sync=wmsSync-VRI[MainActivity]#2
D/OpenGLRenderer(14244): makeCurrent grContext:0xb400006f06eb6060 reset mTextureAvailable
D/mple.darvis_app(14244): MiuiProcessManagerServiceStub setSchedFifo
I/MiuiProcessManagerImpl(14244): setSchedFifo pid:14244, mode:3
W/libc    (14244): Access denied finding property "ro.vendor.display.iris_x7.support"
D/BLASTBufferQueue(14244): [VRI[MainActivity]#0](f:0,a:1) acquireNextBufferLocked size=1080x2400 mFrameNumber=1 applyTransaction=true mTimestamp=431432304332330(auto) mPendingTransactions.size=0 graphicBufferId=61177514164243 transform=0
D/VRI[MainActivity](14244): vri.reportDrawFinished
W/Looper  (14244): PerfMonitor doFrame : time=182ms vsyncFrame=0 latency=469ms procState=-1 historyMsgCount=6
I/HandWritingStubImpl(14244): refreshLastKeyboardType: 1
2
I/HandWritingStubImpl(14244): getCurrentKeyboardType: 1
21
E/BLASTBufferQueue(14244): [SurfaceView[com.example.darvis_app/com.example.darvis_app.MainActivity]#1](f:1,a:4) acquireNextBufferLocked: Can't acquire next buffer. Already acquired max frames 4 max:2 + 2
3
I/ScrollIdentify(14244): on fling
14
E/BLASTBufferQueue(14244): [SurfaceView[com.example.darvis_app/com.example.darvis_app.MainActivity]#1](f:1,a:4) acquireNextBufferLocked: Can't acquire next buffer. Already acquired max frames 4 max:2 + 2
I/ScrollIdentify(14244): on fling
38
E/BLASTBufferQueue(14244): [SurfaceView[com.example.darvis_app/com.example.darvis_app.MainActivity]#1](f:1,a:4) acquireNextBufferLocked: Can't acquire next buffer. Already acquired max frames 4 max:2 + 2
11
I/ScrollIdentify(14244): on fling
E/BLASTBufferQueue(14244): [SurfaceView[com.example.darvis_app/com.example.darvis_app.MainActivity]#1](f:1,a:4) acquireNextBufferLocked: Can't acquire next buffer. Already acquired max frames 4 max:2 + 2
5
I/ScrollIdentify(14244): on fling
14
E/BLASTBufferQueue(14244): [SurfaceView[com.example.darvis_app/com.example.darvis_app.MainActivity]#1](f:1,a:4) acquireNextBufferLocked: Can't acquire next buffer. Already acquired max frames 4 max:2 + 2
2
I/ScrollIdentify(14244): on fling
219
E/BLASTBufferQueue(14244): [SurfaceView[com.example.darvis_app/com.example.darvis_app.MainActivity]#1](f:1,a:4) acquireNextBufferLocked: Can't acquire next buffer. Already acquired max frames 4 max:2 + 2
I/ScrollIdentify(14244): on fling
139
E/BLASTBufferQueue(14244): [SurfaceView[com.example.darvis_app/com.example.darvis_app.MainActivity]#1](f:1,a:4) acquireNextBufferLocked: Can't acquire next buffer. Already acquired max frames 4 max:2 + 2
I/HandWritingStubImpl(14244): refreshLastKeyboardType: 1
I/HandWritingStubImpl(14244): getCurrentKeyboardType: 1
111
E/BLASTBufferQueue(14244): [SurfaceView[com.example.darvis_app/com.example.darvis_app.MainActivity]#1](f:1,a:4) acquireNextBufferLocked: Can't acquire next buffer. Already acquired max frames 4 max:2 + 2
E/LongScreenshotUtils(14244): inLargeScreen false
I/LongScreenshotUtils(14244):  focus:true
I/LongScreenshotUtils(14244): current ratio width:1.0, height:1.0
151
E/BLASTBufferQueue(14244): [SurfaceView[com.example.darvis_app/com.example.darvis_app.MainActivity]#1](f:1,a:4) acquireNextBufferLocked: Can't acquire next buffer. Already acquired max frames 4 max:2 + 2
I/HandWritingStubImpl(14244): refreshLastKeyboardType: 1
I/HandWritingStubImpl(14244): getCurrentKeyboardType: 1
W/libc    (14244): Access denied finding property "ro.vendor.display.iris_x7.support"
W/RenderInspector(14244): DequeueBuffer time out on com.example.darvis_app/com.example.darvis_app.MainActivity, count=4, avg=5 ms, max=10 ms.
W/RenderInspector(14244): DequeueBuffer time out on com.example.darvis_app/com.example.darvis_app.MainActivity, count=1, avg=10 ms, max=10 ms.
95044
E/BLASTBufferQueue(14244): [SurfaceView[com.example.darvis_app/com.example.darvis_app.MainActivity]#1](f:1,a:4) acquireNextBufferLocked: Can't acquire next buffer. Already acquired max frames 4 max:2 + 2
I/MiInputConsumer(14244): optimized resample latency: 20867161 ns
7
I/ScrollIdentify(14244): on fling
W/mple.darvis_app(14244): Reducing the number of considered missed Gc histogram windows from 165 to 100
4
I/ScrollIdentify(14244): on fling
295
E/BLASTBufferQueue(14244): [SurfaceView[com.example.darvis_app/com.example.darvis_app.MainActivity]#1](f:1,a:4) acquireNextBufferLocked: Can't acquire next buffer. Already acquired max frames 4 max:2 + 2
I/HandWritingStubImpl(14244): refreshLastKeyboardType: 1
I/HandWritingStubImpl(14244): getCurrentKeyboardType: 1
212
E/BLASTBufferQueue(14244): [SurfaceView[com.example.darvis_app/com.example.darvis_app.MainActivity]#1](f:1,a:4) acquireNextBufferLocked: Can't acquire next buffer. Already acquired max frames 4 max:2 + 2
E/LongScreenshotUtils(14244): inLargeScreen false
I/LongScreenshotUtils(14244):  focus:true
I/LongScreenshotUtils(14244): current ratio width:1.0, height:1.0
126
E/BLASTBufferQueue(14244): [SurfaceView[com.example.darvis_app/com.example.darvis_app.MainActivity]#1](f:1,a:4) acquireNextBufferLocked: Can't acquire next buffer. Already acquired max frames 4 max:2 + 2
I/HandWritingStubImpl(14244): refreshLastKeyboardType: 1
I/HandWritingStubImpl(14244): getCurrentKeyboardType: 1
W/RenderInspector(14244): DequeueBuffer time out on com.example.darvis_app/com.example.darvis_app.MainActivity, count=2, avg=8 ms, max=9 ms.
2
E/BLASTBufferQueue(14244): [SurfaceView[com.example.darvis_app/com.example.darvis_app.MainActivity]#1](f:1,a:4) acquireNextBufferLocked: Can't acquire next buffer. Already acquired max frames 4 max:2 + 2
D/BLASTBufferQueue(14244): [SurfaceView[com.example.darvis_app/com.example.darvis_app.MainActivity]#1](f:0,a:1) destructor()
D/BufferQueueConsumer(14244): [SurfaceView[com.example.darvis_app/com.example.darvis_app.MainActivity]#1(BLAST Consumer)1](id:37a400000001,api:0,p:-1,c:14244) disconnect
W/MiuiStubRegistry(14244): Failed to collect stub providers in android.magicpointer.util.MiuiMagicPointerUtilsStubHeadManifest$$: /system_ext/framework/miui-framework-pointer-pad.jar not exist
D/BLASTBufferQueue(14244): [VRI[MainActivity]#0](f:0,a:1) destructor()
D/BufferQueueConsumer(14244): [VRI[MainActivity]#0(BLAST Consumer)0](id:37a400000000,api:0,p:-1,c:14244) disconnect
W/Looper  (14244): PerfMonitor doFrame : time=463ms vsyncFrame=0 latency=5ms procState=-1
W/Looper  (14244): PerfMonitor looperActivity : package=com.example.darvis_app/.MainActivity time=28ms latency=471ms  procState=-1  historyMsgCount=1 (msgIndex=1 wall=463ms seq=2118 late=5ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver)
I/Choreographer(14244): Skipped 59 frames!  The application may be doing too much work on its main thread.
W/Looper  (14244): PerfMonitor doFrame : time=2ms vsyncFrame=0 latency=492ms procState=-1 historyMsgCount=2 (msgIndex=1 wall=463ms seq=2118 late=5ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver)
Lost connection to device.

Exited.
