import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:darvis_app/utils/design_tokens.dart';

// Emotion data model
class EmotionData {
  final String name;
  final double percentage;
  final Color color;
  final List<String> examples;

  EmotionData({
    required this.name,
    required this.percentage,
    required this.color,
    required this.examples,
  });
}

// Time range enum
enum TimeRange { thisWeek, thisMonth, threeMonths }

/// TherapyProgressScreen: Shows emotional journey and progress analytics
class TherapyProgressScreen extends StatefulWidget {
  const TherapyProgressScreen({super.key});

  @override
  State<TherapyProgressScreen> createState() => _TherapyProgressScreenState();
}

class _TherapyProgressScreenState extends State<TherapyProgressScreen>
    with TickerProviderStateMixin {
  
  TimeRange _selectedTimeRange = TimeRange.thisMonth;
  bool _isExpanded = false;
  late AnimationController _bubbleController;
  late AnimationController _modalController;
  late Animation<double> _modalAnimation;

  // Mock data for testing
  final Map<TimeRange, List<EmotionData>> _emotionData = {
    TimeRange.thisWeek: [
      EmotionData(
        name: 'Excited',
        percentage: 45.0,
        color: const Color(0xFF22C55E),
        examples: ['New project launch', 'Weekend plans'],
      ),
      EmotionData(
        name: 'Annoyed',
        percentage: 35.0,
        color: const Color(0xFFEAB308),
        examples: ['Traffic delays', 'Work interruptions'],
      ),
      EmotionData(
        name: 'Calm',
        percentage: 20.0,
        color: const Color(0xFF84CC16),
        examples: ['Morning meditation', 'Evening walks'],
      ),
    ],
    TimeRange.thisMonth: [
      EmotionData(
        name: 'Annoyed',
        percentage: 40.0,
        color: const Color(0xFFEAB308),
        examples: ['Work stress', 'Technical issues'],
      ),
      EmotionData(
        name: 'Excited',
        percentage: 30.0,
        color: const Color(0xFF22C55E),
        examples: ['Career opportunities', 'Social events'],
      ),
      EmotionData(
        name: 'Depressed',
        percentage: 20.0,
        color: const Color(0xFF8B5CF6),
        examples: ['Seasonal changes', 'Relationship concerns'],
      ),
      EmotionData(
        name: 'Sad',
        percentage: 10.0,
        color: const Color(0xFF6366F1),
        examples: ['Missing family', 'Old memories'],
      ),
    ],
    TimeRange.threeMonths: [
      EmotionData(
        name: 'Happy',
        percentage: 35.0,
        color: const Color(0xFF06B6D4),
        examples: ['Personal growth', 'New friendships'],
      ),
      EmotionData(
        name: 'Anxious',
        percentage: 25.0,
        color: const Color(0xFFEC4899),
        examples: ['Future planning', 'Health concerns'],
      ),
      EmotionData(
        name: 'Calm',
        percentage: 25.0,
        color: const Color(0xFF84CC16),
        examples: ['Therapy progress', 'Mindfulness practice'],
      ),
      EmotionData(
        name: 'Excited',
        percentage: 15.0,
        color: const Color(0xFF22C55E),
        examples: ['Travel plans', 'Learning new skills'],
      ),
    ],
  };

  final Map<TimeRange, String> _dynamicTitles = {
    TimeRange.thisWeek: "You've Been More Focused",
    TimeRange.thisMonth: "You've Been Happier Lately",
    TimeRange.threeMonths: "Finding More Balance",
  };

  final String _darvisSummary = '''Over the last five sessions, you've spent a lot of time exploring your sense of identity and the pressure you often feel to meet others' expectations. You've opened up about moments where you've doubted your self-worth, particularly in your work and close relationships.

There's been a recurring theme around feeling like you need to prove yourself constantly, which seems to create both motivation and exhaustion. You've also mentioned feeling more aware of your emotional patterns, which is a significant step in your growth journey.

Your willingness to examine these deeper feelings shows real courage and commitment to understanding yourself better.''';

  @override
  void initState() {
    super.initState();
    _bubbleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _modalController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _modalAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _modalController, curve: Curves.easeOut),
    );
    _bubbleController.forward();
  }

  @override
  void dispose() {
    _bubbleController.dispose();
    _modalController.dispose();
    super.dispose();
  }

  void _onTimeRangeChanged(TimeRange newRange) {
    setState(() {
      _selectedTimeRange = newRange;
    });
    _bubbleController.reset();
    _bubbleController.forward();
  }

  void _showExpandedSummary() {
    setState(() {
      _isExpanded = true;
    });
    _modalController.forward();
  }

  void _hideExpandedSummary() {
    _modalController.reverse().then((_) {
      setState(() {
        _isExpanded = false;
      });
    });
  }

  void _onEmotionBubbleTap(EmotionData emotion) {
    HapticFeedback.lightImpact();

    // Clear any existing snackbar immediately for responsive feedback
    ScaffoldMessenger.of(context).clearSnackBars();

    // Show new snackbar immediately
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '${emotion.name}: ${emotion.percentage.toInt()}% of sessions\nRecent examples: ${emotion.examples.join(", ")}',
          style: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
        ),
        backgroundColor: emotion.color.withValues(alpha: 0.9),
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
        ),
      ),
    );
  }

  String get _currentTitle => _dynamicTitles[_selectedTimeRange] ?? "Your Progress";

  List<EmotionData> get _currentEmotions => _emotionData[_selectedTimeRange] ?? [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      body: Stack(
        children: [
          // Background glow
          const _BackgroundGlow(),
          
          // Main content
          SafeArea(
            child: Column(
              children: [
                // Header
                _Header(
                  title: _currentTitle,
                  onBackTap: () => Navigator.of(context).pop(),
                  onMenuTap: () => _showOptionsMenu(),
                ),
                
                // Scrollable content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(
                      horizontal: DesignTokens.spacingMd,
                    ),
                    child: Column(
                      children: [
                        const SizedBox(height: DesignTokens.spacingLg),
                        
                        // Time range selector
                        _TimeRangeSelector(
                          selectedRange: _selectedTimeRange,
                          onRangeChanged: _onTimeRangeChanged,
                        ),
                        
                        const SizedBox(height: DesignTokens.spacingSm),

                        // Emotion bubbles section
                        _EmotionBubblesSection(
                          emotions: _currentEmotions,
                          animation: _bubbleController,
                          onBubbleTap: _onEmotionBubbleTap,
                        ),

                        // Pull BOTH the description and the summary up together
                        Transform.translate(
                          offset: Offset(0, -DesignTokens.spacingXl), // adjust magnitude as needed
                          child: Column(
                            children: [
                              const _DescriptionText(),
                              const SizedBox(height: DesignTokens.spacingSm), // tighter gap
                              _DarvisSummarySection(
                                summary: _darvisSummary,
                                onTap: _showExpandedSummary,
                              ),
                            ],
                          ),
                        ),

                        // Keep downstream spacing consistent (reduced)
                        const SizedBox(height: DesignTokens.spacingMd),
                        
                        // Lift the bottom action button a bit without changing its label
                        Transform.translate(
                          offset: Offset(0, -DesignTokens.spacingMd),
                          child: _BottomActionButton(
                            onTap: () => _navigateToChat(),
                          ),
                        ),

                        // Reduced bottom padding to pull the button area up overall
                        const SizedBox(height: DesignTokens.spacingLg),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Expanded summary modal
          if (_isExpanded)
            _ExpandedSummaryModal(
              summary: _darvisSummary,
              animation: _modalAnimation,
              onDismiss: _hideExpandedSummary,
            ),
        ],
      ),
    );
  }

  void _showOptionsMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _OptionsMenu(),
    );
  }

  void _navigateToChat() {
    HapticFeedback.mediumImpact();
    // Navigate to chat with therapy context
    print('Navigate to chat with therapy context');
  }
}

// Header Component
class _Header extends StatelessWidget {
  final String title;
  final VoidCallback onBackTap;
  final VoidCallback onMenuTap;

  const _Header({
    required this.title,
    required this.onBackTap,
    required this.onMenuTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingLg,
        vertical: DesignTokens.spacingLg,
      ),
      child: Column(
        children: [
          const SizedBox(height: DesignTokens.spacingMd),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: onBackTap,
                child: Container(
                  padding: const EdgeInsets.all(DesignTokens.spacingSm),
                  decoration: BoxDecoration(
                    color: DesignTokens.backgroundCard.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                  ),
                  child: const Icon(
                    Icons.arrow_back,
                    color: DesignTokens.iconPrimary,
                    size: 24,
                  ),
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
                  child: ShaderMask(
                    shaderCallback: (bounds) => DesignTokens.appNameHeaderGradient.createShader(bounds),
                    child: Text(
                      title,
                      style: DesignTokens.therapySessionTitleStyle.copyWith(
                        color: Colors.white,
                        fontSize: 32,
                        fontWeight: FontWeight.w700,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ),
              GestureDetector(
                onTap: onMenuTap,
                child: Container(
                  padding: const EdgeInsets.all(DesignTokens.spacingSm),
                  decoration: BoxDecoration(
                    color: DesignTokens.backgroundCard.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                  ),
                  child: const Icon(
                    Icons.more_vert,
                    color: DesignTokens.iconPrimary,
                    size: 24,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// Time Range Selector Component
class _TimeRangeSelector extends StatelessWidget {
  final TimeRange selectedRange;
  final Function(TimeRange) onRangeChanged;

  const _TimeRangeSelector({
    required this.selectedRange,
    required this.onRangeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: DesignTokens.backgroundCard.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
      ),
      child: Row(
        children: [
          _TimeRangeOption(
            text: 'This Week',
            isSelected: selectedRange == TimeRange.thisWeek,
            onTap: () => onRangeChanged(TimeRange.thisWeek),
          ),
          _TimeRangeOption(
            text: 'This Month',
            isSelected: selectedRange == TimeRange.thisMonth,
            onTap: () => onRangeChanged(TimeRange.thisMonth),
          ),
          _TimeRangeOption(
            text: '3 Months',
            isSelected: selectedRange == TimeRange.threeMonths,
            onTap: () => onRangeChanged(TimeRange.threeMonths),
          ),
        ],
      ),
    );
  }
}

// Time Range Option Component
class _TimeRangeOption extends StatelessWidget {
  final String text;
  final bool isSelected;
  final VoidCallback onTap;

  const _TimeRangeOption({
    required this.text,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(
            vertical: DesignTokens.spacingSm,
            horizontal: DesignTokens.spacingMd,
          ),
          decoration: BoxDecoration(
            color: isSelected
                ? DesignTokens.primaryInteractiveBlue
                : Colors.transparent,
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          ),
          child: Text(
            text,
            style: DesignTokens.bodyStyle.copyWith(
              color: isSelected
                  ? DesignTokens.textPrimary
                  : DesignTokens.textMuted,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}

// Emotion Bubbles Section Component
class _EmotionBubblesSection extends StatelessWidget {
  final List<EmotionData> emotions;
  final AnimationController animation;
  final Function(EmotionData) onBubbleTap;

  const _EmotionBubblesSection({
    required this.emotions,
    required this.animation,
    required this.onBubbleTap,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final containerHeight = (screenHeight * 0.4).clamp(200.0, 400.0);
    final containerWidth = screenWidth - (DesignTokens.spacingMd * 2);

    return SizedBox(
      height: containerHeight,
      width: containerWidth,
      child: AnimatedBuilder(
        animation: animation,
        builder: (context, child) {
          // Find the largest emotion for center positioning
          final largestEmotion = emotions.reduce((a, b) =>
            a.percentage > b.percentage ? a : b);

          return Stack(
            children: emotions.asMap().entries.map((entry) {
              final index = entry.key;
              final emotion = entry.value;
              return _EmotionBubble(
                emotion: emotion,
                containerWidth: containerWidth,
                containerHeight: containerHeight,
                index: index,
                totalBubbles: emotions.length,
                animation: animation,
                isLargest: emotion == largestEmotion,
                onTap: () => onBubbleTap(emotion),
              );
            }).toList(),
          );
        },
      ),
    );
  }
}

// Individual Emotion Bubble Component
class _EmotionBubble extends StatelessWidget {
  final EmotionData emotion;
  final double containerWidth;
  final double containerHeight;
  final int index;
  final int totalBubbles;
  final Animation<double> animation;
  final bool isLargest;
  final VoidCallback onTap;

  const _EmotionBubble({
    required this.emotion,
    required this.containerWidth,
    required this.containerHeight,
    required this.index,
    required this.totalBubbles,
    required this.animation,
    required this.isLargest,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // Calculate bubble size based on percentage
    double bubbleSize;
    if (emotion.percentage <= 15) {
      bubbleSize = 60 + (emotion.percentage / 15) * 20; // 60-80px
    } else if (emotion.percentage <= 35) {
      bubbleSize = 80 + ((emotion.percentage - 15) / 20) * 40; // 80-120px
    } else if (emotion.percentage <= 60) {
      bubbleSize = 120 + ((emotion.percentage - 35) / 25) * 40; // 120-160px
    } else {
      bubbleSize = 160 + ((emotion.percentage - 60) / 40) * 20; // 160-180px (capped)
    }

    // Auto-scale if bubbles exceed container
    final maxBubbleSize = containerHeight * 0.4;
    if (bubbleSize > maxBubbleSize) {
      bubbleSize = maxBubbleSize;
    }

    // Grid-based center-focused positioning
    final position = _calculateGridPosition(
      containerWidth: containerWidth,
      containerHeight: containerHeight,
      bubbleSize: bubbleSize,
      index: index,
      totalBubbles: totalBubbles,
      isLargest: isLargest,
    );

    final left = position['x']!;
    final top = position['y']!;

    return AnimatedPositioned(
      duration: Duration(milliseconds: 300 + (index * 100)),
      left: left,
      top: top,
      child: Transform.scale(
        scale: animation.value,
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            width: bubbleSize,
            height: bubbleSize,
            decoration: BoxDecoration(
              color: emotion.color,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: emotion.color.withValues(alpha: 0.3),
                  blurRadius: 15,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Center(
              child: Text(
                emotion.name,
                style: DesignTokens.bodyStyle.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: bubbleSize > 100 ? 16 : 14,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Map<String, double> _calculateGridPosition({
    required double containerWidth,
    required double containerHeight,
    required double bubbleSize,
    required int index,
    required int totalBubbles,
    required bool isLargest,
  }) {
    // Center coordinates
    final centerX = (containerWidth - bubbleSize) / 2;
    final centerY = (containerHeight - bubbleSize) / 2;

    // If this is the largest bubble, place it at center
    if (isLargest) {
      return {
        'x': centerX + _getArtisticOffset(),
        'y': centerY + _getArtisticOffset(),
      };
    }

    // Grid positions around center for other bubbles
    final gridPositions = _getGridPositions(
      containerWidth: containerWidth,
      containerHeight: containerHeight,
      bubbleSize: bubbleSize,
      centerX: centerX,
      centerY: centerY,
    );

    // Use index to select position, but ensure we don't exceed available positions
    final positionIndex = index % gridPositions.length;
    final basePosition = gridPositions[positionIndex];

    return {
      'x': basePosition['x']! + _getArtisticOffset(),
      'y': basePosition['y']! + _getArtisticOffset(),
    };
  }

  List<Map<String, double>> _getGridPositions({
    required double containerWidth,
    required double containerHeight,
    required double bubbleSize,
    required double centerX,
    required double centerY,
  }) {
    // Calculate safe distances from center to avoid overlap
    final safeDistanceX = bubbleSize * 0.8 + 20; // 20px minimum spacing
    final safeDistanceY = bubbleSize * 0.8 + 20;

    // Ensure positions stay within container bounds
    final minX = bubbleSize / 2;
    final maxX = containerWidth - bubbleSize / 2;
    final minY = bubbleSize / 2;
    final maxY = containerHeight - bubbleSize / 2;

    final positions = [
      // Top positions
      {
        'x': (centerX - safeDistanceX).clamp(minX, maxX), // Top-left
        'y': (centerY - safeDistanceY).clamp(minY, maxY),
      },
      {
        'x': (centerX + safeDistanceX).clamp(minX, maxX), // Top-right
        'y': (centerY - safeDistanceY).clamp(minY, maxY),
      },

      // Side positions
      {
        'x': (centerX - safeDistanceX * 1.3).clamp(minX, maxX), // Left
        'y': centerY.clamp(minY, maxY),
      },
      {
        'x': (centerX + safeDistanceX * 1.3).clamp(minX, maxX), // Right
        'y': centerY.clamp(minY, maxY),
      },

      // Bottom positions
      {
        'x': (centerX - safeDistanceX).clamp(minX, maxX), // Bottom-left
        'y': (centerY + safeDistanceY).clamp(minY, maxY),
      },
      {
        'x': (centerX + safeDistanceX).clamp(minX, maxX), // Bottom-right
        'y': (centerY + safeDistanceY).clamp(minY, maxY),
      },
    ];

    return positions;
  }

  double _getArtisticOffset() {
    // Small random-like offset for organic feel (5-10px)
    // Using index-based calculation for consistency
    final offsets = [-8.0, -5.0, 0.0, 5.0, 8.0];
    return offsets[index % offsets.length];
  }
}

// Description Text Component
class _DescriptionText extends StatelessWidget {
  const _DescriptionText();

  @override
  Widget build(BuildContext context) {
    return Text(
      'These circles show how often each emotion has come up in your recent sessions. Larger circles mean higher presence.',
      style: DesignTokens.bodyStyle.copyWith(
        color: DesignTokens.textSecondary,
        fontSize: 14,
        height: 1.4,
      ),
      textAlign: TextAlign.center,
    );
  }
}

// Darvis Summary Section Component
class _DarvisSummarySection extends StatelessWidget {
  final String summary;
  final VoidCallback onTap;

  const _DarvisSummarySection({
    required this.summary,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final previewText = summary.length > 150
        ? '${summary.substring(0, 150)}...'
        : summary;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(DesignTokens.spacingLg),
        decoration: BoxDecoration(
          color: DesignTokens.backgroundCard.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
          border: Border.all(
            color: DesignTokens.primaryAccentBlue.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Darvis Summary',
              style: DesignTokens.cardTitleStyle.copyWith(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: DesignTokens.spacingMd),
            Text(
              previewText,
              style: DesignTokens.onboardingBodyStyle.copyWith(
                color: DesignTokens.textSecondary,
                height: 1.5,
              ),
            ),
            if (summary.length > 150) ...[
              const SizedBox(height: DesignTokens.spacingSm),
              Text(
                'Tap to read more',
                style: DesignTokens.bodyStyle.copyWith(
                  color: DesignTokens.primaryInteractiveBlue,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// Bottom Action Button Component
class _BottomActionButton extends StatelessWidget {
  final VoidCallback onTap;

  const _BottomActionButton({required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(
          vertical: DesignTokens.spacingMd,
        ),
        decoration: BoxDecoration(
          color: DesignTokens.primaryButtonBackground,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
          boxShadow: [
            BoxShadow(
              color: DesignTokens.primaryButtonBackground.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Text(
          'Chat With Darvis About This',
          style: DesignTokens.primaryButtonTextStyle.copyWith(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

// Expanded Summary Modal Component
class _ExpandedSummaryModal extends StatelessWidget {
  final String summary;
  final Animation<double> animation;
  final VoidCallback onDismiss;

  const _ExpandedSummaryModal({
    required this.summary,
    required this.animation,
    required this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onDismiss,
      child: AnimatedBuilder(
        animation: animation,
        builder: (context, child) {
          return Container(
            color: Colors.black.withValues(alpha: 0.5 * animation.value),
            child: Align(
              alignment: Alignment.bottomCenter,
              child: Transform.translate(
                offset: Offset(0, (1 - animation.value) * 300),
                child: Container(
                  width: double.infinity,
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.7,
                  ),
                  decoration: BoxDecoration(
                    gradient: DesignTokens.therapyNeutralBlurGradient,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                  ),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
                    child: Container(
                      decoration: BoxDecoration(
                        color: DesignTokens.backgroundCard.withValues(alpha: 0.8),
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(24),
                          topRight: Radius.circular(24),
                        ),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Handle bar
                          Container(
                            margin: const EdgeInsets.only(top: 12),
                            width: 40,
                            height: 4,
                            decoration: BoxDecoration(
                              color: DesignTokens.textMuted,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),

                          // Content
                          Flexible(
                            child: SingleChildScrollView(
                              padding: const EdgeInsets.all(DesignTokens.spacingLg),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Full Darvis Summary',
                                    style: DesignTokens.cardTitleStyle.copyWith(
                                      fontSize: 20,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                  const SizedBox(height: DesignTokens.spacingLg),
                                  Text(
                                    summary,
                                    style: DesignTokens.onboardingBodyStyle.copyWith(
                                      color: DesignTokens.textPrimary,
                                      height: 1.6,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

// Options Menu Component
class _OptionsMenu extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: DesignTokens.backgroundCard,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(DesignTokens.borderRadiusLg),
          topRight: Radius.circular(DesignTokens.borderRadiusLg),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: DesignTokens.textMuted,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            const SizedBox(height: DesignTokens.spacingLg),

            // Export Progress option
            _MenuOption(
              icon: Icons.file_download,
              title: 'Export Progress',
              subtitle: 'Download your emotional journey data',
              onTap: () {
                Navigator.pop(context);
                _exportProgress();
              },
            ),

            // Settings option
            _MenuOption(
              icon: Icons.settings,
              title: 'Settings',
              subtitle: 'Customize your progress tracking',
              onTap: () {
                Navigator.pop(context);
                _openSettings();
              },
            ),

            const SizedBox(height: DesignTokens.spacingLg),
          ],
        ),
      ),
    );
  }

  void _exportProgress() {
    print('Export progress functionality');
  }

  void _openSettings() {
    print('Open settings functionality');
  }
}

// Menu Option Component
class _MenuOption extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const _MenuOption({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingLg,
          vertical: DesignTokens.spacingMd,
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(DesignTokens.spacingSm),
              decoration: BoxDecoration(
                color: DesignTokens.primaryAccentBlue.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusSm),
              ),
              child: Icon(
                icon,
                color: DesignTokens.primaryInteractiveBlue,
                size: 20,
              ),
            ),
            const SizedBox(width: DesignTokens.spacingMd),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: DesignTokens.cardTitleStyle.copyWith(
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: DesignTokens.bodyStyle.copyWith(
                      color: DesignTokens.textMuted,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.chevron_right,
              color: DesignTokens.textMuted,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }
}

// Background Glow Component
class _BackgroundGlow extends StatelessWidget {
  const _BackgroundGlow();

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.1,
        decoration: const BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Color.fromARGB(255, 9, 38, 107),
              blurRadius: 150.0,
              spreadRadius: 50.0,
            ),
          ],
        ),
      ),
    );
  }
}
