import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:darvis_app/utils/design_tokens.dart';
import 'notes_screen.dart';

/// Individual Note View Screen
class NoteViewScreen extends StatefulWidget {
  final Note note;

  const NoteViewScreen({
    super.key,
    required this.note,
  });

  @override
  State<NoteViewScreen> createState() => _NoteViewScreenState();
}

class _NoteViewScreenState extends State<NoteViewScreen> {
  late TextEditingController _titleController;
  late TextEditingController _contentController;
  late Note _note;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _note = widget.note;
    _titleController = TextEditingController(text: _note.title);
    _contentController = TextEditingController(text: _note.content);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  void _toggleEdit() {
    setState(() {
      _isEditing = !_isEditing;
    });
  }

  void _saveNote() {
    setState(() {
      _note.title = _titleController.text;
      _note.content = _contentController.text;
      _note.updatedAt = DateTime.now();
      _isEditing = false;
    });
    
    // Auto-save functionality would go here
    HapticFeedback.lightImpact();
  }

  void _showOptionsMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: DesignTokens.backgroundCard,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(DesignTokens.borderRadiusLg),
        ),
      ),
      builder: (context) => _OptionsBottomSheet(
        note: _note,
        onEdit: _toggleEdit,
        onDelete: () {
          Navigator.of(context).pop();
          Navigator.of(context).pop();
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _note.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: DesignTokens.iconPrimary),
          onPressed: () {
            if (_isEditing) {
              _saveNote();
            }
            Navigator.of(context).pop();
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert, color: DesignTokens.iconPrimary),
            onPressed: _showOptionsMenu,
          ),
        ],
      ),
      body: SafeArea(
        top: false, // Avoid extra top inset; AppBar already handles status bar
        child: Padding(
          padding: const EdgeInsets.only(
            left: DesignTokens.spacingLg,
            right: DesignTokens.spacingLg,
            top: 0, // Pull title much closer to the AppBar
            bottom: DesignTokens.spacingLg,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title
              TextField(
                controller: _titleController,
                enabled: _isEditing,
                style: DesignTokens.cardTitleStyle.copyWith(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: _note.backgroundColor == DesignTokens.backgroundTextInput
                      ? DesignTokens.textOnLightBackground
                      : DesignTokens.textPrimary,
                ),
                decoration: InputDecoration(
                  hintText: 'Note title',
                  hintStyle: DesignTokens.cardTitleStyle.copyWith(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: _note.backgroundColor == DesignTokens.backgroundTextInput
                        ? DesignTokens.textOnLightBackground.withValues(alpha: 0.6)
                        : DesignTokens.textSecondary,
                  ),
                  border: InputBorder.none,
                  filled: false,
                  fillColor: Colors.transparent,
                  isCollapsed: true, // Remove default TextField vertical padding
                ),
                maxLines: 2,
                onChanged: (value) {
                  if (_isEditing) {
                    // Auto-save after 2 seconds of inactivity
                  }
                },
              ),
              
              const SizedBox(height: DesignTokens.spacingXs), // Short gap between title and body
              
              // Content
              Expanded(
                child: TextField(
                  controller: _contentController,
                  enabled: _isEditing,
                  style: DesignTokens.bodyStyle.copyWith(
                    color: _note.backgroundColor == DesignTokens.backgroundTextInput
                        ? DesignTokens.textOnLightBackground
                        : DesignTokens.textPrimary,
                  ),
                  decoration: InputDecoration(
                    hintText: 'Start writing...',
                    hintStyle: DesignTokens.bodyStyle.copyWith(
                      color: _note.backgroundColor == DesignTokens.backgroundTextInput
                          ? DesignTokens.textOnLightBackground.withValues(alpha: 0.6)
                          : DesignTokens.textSecondary,
                    ),
                    border: InputBorder.none,
                    filled: false,
                    fillColor: Colors.transparent,
                    isCollapsed: true, // Remove default padding so body starts closer
                  ),
                  maxLines: null,
                  expands: true,
                  textAlignVertical: TextAlignVertical.top,
                  onChanged: (value) {
                    if (_isEditing) {
                      // Auto-save after 2 seconds of inactivity
                    }
                  },
                ),
              ),
              
              // Tags section
              if (_note.tags.isNotEmpty) ...[
                const SizedBox(height: DesignTokens.spacingMd),
                Wrap(
                  spacing: DesignTokens.spacingSm,
                  runSpacing: DesignTokens.spacingSm,
                  children: _note.tags.map((tag) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: DesignTokens.spacingMd,
                        vertical: DesignTokens.spacingSm,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        border: Border.all(
                          color: _note.backgroundColor == DesignTokens.backgroundTextInput
                              ? DesignTokens.textOnLightBackground.withValues(alpha: 0.3)
                              : DesignTokens.primaryAccentBlue.withValues(alpha: 0.5),
                          width: 1.5,
                        ),
                        borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                      ),
                      child: Text(
                        tag,
                        style: DesignTokens.bodyStyle.copyWith(
                          color: _note.backgroundColor == DesignTokens.backgroundTextInput
                              ? DesignTokens.textOnLightBackground
                              : DesignTokens.textPrimary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ],
          ),
        ),
      ),
      floatingActionButton: _isEditing
          ? FloatingActionButton(
              onPressed: _saveNote,
              backgroundColor: DesignTokens.primaryInteractiveBlue,
              child: const Icon(Icons.check, color: Colors.white),
            )
          : FloatingActionButton(
              onPressed: _toggleEdit,
              backgroundColor: DesignTokens.primaryInteractiveBlue,
              child: const Icon(Icons.edit, color: Colors.white),
            ),
    );
  }
}

// Options Bottom Sheet
class _OptionsBottomSheet extends StatelessWidget {
  final Note note;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const _OptionsBottomSheet({
    required this.note,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingLg),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _OptionItem(
            icon: Icons.edit,
            title: 'Edit',
            onTap: () {
              Navigator.of(context).pop();
              onEdit();
            },
          ),
          _OptionItem(
            icon: Icons.delete,
            title: 'Delete',
            onTap: onDelete,
            isDestructive: true,
          ),
          _OptionItem(
            icon: Icons.label,
            title: 'Add Tags',
            onTap: () {
              Navigator.of(context).pop();
              // Add tags functionality
            },
          ),
          _OptionItem(
            icon: note.isPinned ? Icons.push_pin_outlined : Icons.push_pin,
            title: note.isPinned ? 'Unpin' : 'Pin',
            onTap: () {
              Navigator.of(context).pop();
              // Pin/unpin functionality
            },
          ),
        ],
      ),
    );
  }
}

// Option Item
class _OptionItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final bool isDestructive;

  const _OptionItem({
    required this.icon,
    required this.title,
    required this.onTap,
    this.isDestructive = false,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? Colors.red : DesignTokens.iconPrimary,
      ),
      title: Text(
        title,
        style: DesignTokens.bodyStyle.copyWith(
          color: isDestructive ? Colors.red : DesignTokens.textPrimary,
        ),
      ),
      onTap: onTap,
    );
  }
}
