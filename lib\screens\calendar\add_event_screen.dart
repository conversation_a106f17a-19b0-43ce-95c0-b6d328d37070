import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:darvis_app/utils/design_tokens.dart';
import 'package:intl/intl.dart';

class AddEventScreen extends StatefulWidget {
  const AddEventScreen({super.key});

  @override
  State<AddEventScreen> createState() => _AddEventScreenState();
}

class _AddEventScreenState extends State<AddEventScreen> with TickerProviderStateMixin {
  final _eventNameController = TextEditingController();
  final _locationController = TextEditingController();
  
  DateTime? _selectedDate;
  TimeOfDay? _selectedTime;
  bool _isLoading = false;
  bool _showDatePicker = false;
  bool _showTimePicker = false;
  
  late AnimationController _loadingController;
  late AnimationController _datePickerController;
  late AnimationController _timePickerController;
  
  @override
  void initState() {
    super.initState();
    _loadingController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _datePickerController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _timePickerController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _eventNameController.dispose();
    _locationController.dispose();
    _loadingController.dispose();
    _datePickerController.dispose();
    _timePickerController.dispose();
    super.dispose();
  }

  void _toggleDatePicker() {
    setState(() {
      _showDatePicker = !_showDatePicker;
      _showTimePicker = false; // Close time picker if open
    });
    
    if (_showDatePicker) {
      _datePickerController.forward();
    } else {
      _datePickerController.reverse();
    }
  }

  void _toggleTimePicker() {
    setState(() {
      _showTimePicker = !_showTimePicker;
      _showDatePicker = false; // Close date picker if open
    });
    
    if (_showTimePicker) {
      _timePickerController.forward();
    } else {
      _timePickerController.reverse();
    }
  }

  void _selectDate(DateTime date) {
    setState(() {
      _selectedDate = date;
      _showDatePicker = false;
    });
    _datePickerController.reverse();
  }

  void _selectTime(TimeOfDay time) {
    setState(() {
      _selectedTime = time;
      _showTimePicker = false;
    });
    _timePickerController.reverse();
  }

  Future<void> _saveEvent() async {
    if (_eventNameController.text.isEmpty) return;
    
    setState(() {
      _isLoading = true;
    });
    
    _loadingController.repeat();
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));
    
    _loadingController.stop();
    
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      body: Stack(
        children: [
          SafeArea(
            child: Column(
              children: [
                _Header(onBackTap: () => Navigator.of(context).pop()),
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(DesignTokens.spacingMd),
                    child: Column(
                      children: [
                        const SizedBox(height: DesignTokens.spacingLg),
                        _EventNameField(controller: _eventNameController),
                        const SizedBox(height: DesignTokens.spacingMd),
                        _DateField(
                          selectedDate: _selectedDate,
                          onTap: _toggleDatePicker,
                        ),
                        const SizedBox(height: DesignTokens.spacingMd),
                        _LocationField(controller: _locationController),
                        const SizedBox(height: DesignTokens.spacingMd),
                        _TimeField(
                          selectedTime: _selectedTime,
                          onTap: _toggleTimePicker,
                        ),
                        const SizedBox(height: DesignTokens.spacingXl),
                        _SaveButton(
                          onTap: _saveEvent,
                          isLoading: _isLoading,
                          loadingController: _loadingController,
                        ),
                      ],
                    ),
                  ),
                ),
                _BottomNavBar(selectedIndex: 1),
              ],
            ),
          ),
          
          // Date Picker Overlay
          if (_showDatePicker)
            _DatePickerOverlay(
              controller: _datePickerController,
              onDateSelected: _selectDate,
              onClose: _toggleDatePicker,
            ),
            
          // Time Picker Overlay
          if (_showTimePicker)
            _TimePickerOverlay(
              controller: _timePickerController,
              onTimeSelected: _selectTime,
              onClose: _toggleTimePicker,
            ),
        ],
      ),
    );
  }
}

// Header Component
class _Header extends StatelessWidget {
  final VoidCallback onBackTap;
  
  const _Header({required this.onBackTap});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: false,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingMd,
          vertical: DesignTokens.spacingSm,
        ),
        child: Column(
          children: [
            const SizedBox(height: DesignTokens.spacingXl), // Increased top spacing for better separation
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: onBackTap,
                  child: const Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const _GradientTitle(text: 'Add an Event'),
                const SizedBox(width: 24), // Balance the back button
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Gradient Title Component (reused from calendar)
class _GradientTitle extends StatelessWidget {
  final String text;
  
  const _GradientTitle({required this.text});

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      shaderCallback: (bounds) => DesignTokens.appNameHeaderGradient.createShader(bounds),
      child: Text(
        text,
        style: DesignTokens.appNameStyle.copyWith(
          color: Colors.white,
          fontSize: 35,
        ),
      ),
    );
  }
}

// Event Name Field
class _EventNameField extends StatelessWidget {
  final TextEditingController controller;
  
  const _EventNameField({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Event Name', style: DesignTokens.formLabelStyle),
        const SizedBox(height: DesignTokens.spacingSm),
        TextField(
          controller: controller,
          style: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
          decoration: InputDecoration(
            filled: true,
            fillColor: DesignTokens.formFieldBackground,
            hintText: "What's the occasion?",
            hintStyle: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textMuted),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              borderSide: BorderSide.none,
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingMd,
              vertical: DesignTokens.spacingMd,
            ),
          ),
        ),
      ],
    );
  }
}

// Date Field
class _DateField extends StatelessWidget {
  final DateTime? selectedDate;
  final VoidCallback onTap;

  const _DateField({required this.selectedDate, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Date', style: DesignTokens.formLabelStyle),
        const SizedBox(height: DesignTokens.spacingSm),
        GestureDetector(
          onTap: onTap,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingMd,
              vertical: DesignTokens.spacingMd,
            ),
            decoration: BoxDecoration(
              color: DesignTokens.formFieldBackground,
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
            ),
            child: Text(
              selectedDate != null
                ? DateFormat('MMM dd, yyyy').format(selectedDate!)
                : 'Pick a day to make memories.',
              style: DesignTokens.bodyStyle.copyWith(
                color: selectedDate != null
                  ? DesignTokens.textPrimary
                  : DesignTokens.textMuted,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

// Location Field
class _LocationField extends StatelessWidget {
  final TextEditingController controller;

  const _LocationField({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Location', style: DesignTokens.formLabelStyle),
        const SizedBox(height: DesignTokens.spacingSm),
        TextField(
          controller: controller,
          style: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
          decoration: InputDecoration(
            filled: true,
            fillColor: DesignTokens.formFieldBackground,
            hintText: "Where's the magic happening?",
            hintStyle: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textMuted),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              borderSide: BorderSide.none,
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingMd,
              vertical: DesignTokens.spacingMd,
            ),
          ),
        ),
      ],
    );
  }
}

// Time Field
class _TimeField extends StatelessWidget {
  final TimeOfDay? selectedTime;
  final VoidCallback onTap;

  const _TimeField({required this.selectedTime, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Time', style: DesignTokens.formLabelStyle),
        const SizedBox(height: DesignTokens.spacingSm),
        GestureDetector(
          onTap: onTap,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingMd,
              vertical: DesignTokens.spacingMd,
            ),
            decoration: BoxDecoration(
              color: DesignTokens.formFieldBackground,
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
            ),
            child: Text(
              selectedTime != null
                ? selectedTime!.format(context)
                : 'What time is it starting?',
              style: DesignTokens.bodyStyle.copyWith(
                color: selectedTime != null
                  ? DesignTokens.textPrimary
                  : DesignTokens.textMuted,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

// Save Button
class _SaveButton extends StatelessWidget {
  final VoidCallback onTap;
  final bool isLoading;
  final AnimationController loadingController;

  const _SaveButton({
    required this.onTap,
    required this.isLoading,
    required this.loadingController,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: GestureDetector(
        onTap: isLoading ? null : onTap,
        child: Container(
          width: 150,
          height: 45,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: isLoading ? Colors.transparent : DesignTokens.primaryButtonBackground,
            gradient: isLoading ? DesignTokens.appNameHeaderGradient : null,
            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          ),
          child: isLoading
            ? RotationTransition(
                turns: loadingController,
                child: const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              )
            : const Text('Save Event', style: DesignTokens.primaryButtonTextStyle),
        ),
      ),
    );
  }
}

// Date Picker Overlay
class _DatePickerOverlay extends StatefulWidget {
  final AnimationController controller;
  final Function(DateTime) onDateSelected;
  final VoidCallback onClose;

  const _DatePickerOverlay({
    required this.controller,
    required this.onDateSelected,
    required this.onClose,
  });

  @override
  State<_DatePickerOverlay> createState() => _DatePickerOverlayState();
}

class _DatePickerOverlayState extends State<_DatePickerOverlay> {
  late DateTime _currentDate;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _currentDate = DateTime.now();
    _pageController = PageController(initialPage: 0);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _previousMonth() {
    setState(() {
      _currentDate = DateTime(_currentDate.year, _currentDate.month - 1);
    });
  }

  void _nextMonth() {
    setState(() {
      _currentDate = DateTime(_currentDate.year, _currentDate.month + 1);
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onClose,
      child: Container(
        color: Colors.black54,
        child: Center(
          child: GestureDetector(
            onTap: () {}, // Prevent closing when tapping the picker
            child: SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0, 1),
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: widget.controller,
                curve: Curves.easeOutCubic,
              )),
              child: Container(
                margin: const EdgeInsets.all(DesignTokens.spacingLg),
                padding: const EdgeInsets.all(DesignTokens.spacingLg),
                decoration: BoxDecoration(
                  color: DesignTokens.backgroundCard,
                  borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Month/Year Header with Navigation
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                          onPressed: _previousMonth,
                          icon: const Icon(Icons.chevron_left, color: Colors.white),
                        ),
                        Text(
                          DateFormat('MMMM yyyy').format(_currentDate),
                          style: DesignTokens.cardTitleStyle,
                        ),
                        IconButton(
                          onPressed: _nextMonth,
                          icon: const Icon(Icons.chevron_right, color: Colors.white),
                        ),
                      ],
                    ),
                    const SizedBox(height: DesignTokens.spacingMd),

                    // Month Grid
                    _MonthGrid(
                      currentDate: _currentDate,
                      onDateSelected: widget.onDateSelected,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// Month Grid Component
class _MonthGrid extends StatelessWidget {
  final DateTime currentDate;
  final Function(DateTime) onDateSelected;

  const _MonthGrid({
    required this.currentDate,
    required this.onDateSelected,
  });

  @override
  Widget build(BuildContext context) {
    final firstDayOfMonth = DateTime(currentDate.year, currentDate.month, 1);
    final lastDayOfMonth = DateTime(currentDate.year, currentDate.month + 1, 0);
    final firstDayWeekday = firstDayOfMonth.weekday % 7;
    final daysInMonth = lastDayOfMonth.day;

    return Column(
      children: [
        // Days of week header
        Row(
          children: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
              .map((day) => Expanded(
                    child: Center(
                      child: Text(
                        day,
                        style: DesignTokens.bodyStyle.copyWith(
                          color: DesignTokens.textMuted,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ))
              .toList(),
        ),
        const SizedBox(height: DesignTokens.spacingSm),

        // Calendar Grid
        ...List.generate(6, (weekIndex) {
          return Row(
            children: List.generate(7, (dayIndex) {
              final dayNumber = weekIndex * 7 + dayIndex - firstDayWeekday + 1;

              if (dayNumber < 1 || dayNumber > daysInMonth) {
                return const Expanded(child: SizedBox(height: 40));
              }

              final date = DateTime(currentDate.year, currentDate.month, dayNumber);
              final isToday = _isSameDay(date, DateTime.now());

              return Expanded(
                child: GestureDetector(
                  onTap: () => onDateSelected(date),
                  child: Container(
                    height: 40,
                    margin: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: isToday ? DesignTokens.primaryInteractiveBlue : Colors.transparent,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        dayNumber.toString(),
                        style: DesignTokens.bodyStyle.copyWith(
                          color: isToday ? Colors.white : DesignTokens.textPrimary,
                          fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }),
          );
        }).where((row) {
          // Only show rows that have at least one valid day
          return true;
        }).take(6),
      ],
    );
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }
}

// Time Picker Overlay
class _TimePickerOverlay extends StatefulWidget {
  final AnimationController controller;
  final Function(TimeOfDay) onTimeSelected;
  final VoidCallback onClose;

  const _TimePickerOverlay({
    required this.controller,
    required this.onTimeSelected,
    required this.onClose,
  });

  @override
  State<_TimePickerOverlay> createState() => _TimePickerOverlayState();
}

class _TimePickerOverlayState extends State<_TimePickerOverlay> {
  late FixedExtentScrollController _hourController;
  late FixedExtentScrollController _minuteController;
  late FixedExtentScrollController _periodController;

  int _selectedHour = 12;
  int _selectedMinute = 0;
  int _selectedPeriod = 0; // 0 = AM, 1 = PM

  @override
  void initState() {
    super.initState();
    final now = TimeOfDay.now();
    _selectedHour = now.hourOfPeriod == 0 ? 12 : now.hourOfPeriod;
    _selectedMinute = now.minute;
    _selectedPeriod = now.period == DayPeriod.am ? 0 : 1;

    _hourController = FixedExtentScrollController(initialItem: _selectedHour - 1);
    _minuteController = FixedExtentScrollController(initialItem: _selectedMinute);
    _periodController = FixedExtentScrollController(initialItem: _selectedPeriod);
  }

  @override
  void dispose() {
    _hourController.dispose();
    _minuteController.dispose();
    _periodController.dispose();
    super.dispose();
  }

  void _confirmTime() {
    final hour = _selectedPeriod == 0
      ? (_selectedHour == 12 ? 0 : _selectedHour)
      : (_selectedHour == 12 ? 12 : _selectedHour + 12);

    widget.onTimeSelected(TimeOfDay(hour: hour, minute: _selectedMinute));
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onClose,
      child: Container(
        color: Colors.black54,
        child: Center(
          child: GestureDetector(
            onTap: () {}, // Prevent closing when tapping the picker
            child: SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0, 1),
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: widget.controller,
                curve: Curves.easeOutCubic,
              )),
              child: Container(
                margin: const EdgeInsets.all(DesignTokens.spacingLg),
                padding: const EdgeInsets.all(DesignTokens.spacingLg),
                decoration: BoxDecoration(
                  color: DesignTokens.backgroundCard,
                  borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Select Time',
                      style: DesignTokens.cardTitleStyle,
                    ),
                    const SizedBox(height: DesignTokens.spacingLg),

                    // Time Picker Wheels
                    SizedBox(
                      height: 200,
                      child: Row(
                        children: [
                          // Hours
                          Expanded(
                            child: ListWheelScrollView.useDelegate(
                              controller: _hourController,
                              itemExtent: 40,
                              physics: const FixedExtentScrollPhysics(),
                              onSelectedItemChanged: (index) {
                                setState(() {
                                  _selectedHour = index + 1;
                                });
                              },
                              childDelegate: ListWheelChildBuilderDelegate(
                                builder: (context, index) {
                                  if (index < 0 || index >= 12) return null;
                                  final hour = index + 1;
                                  return Center(
                                    child: Text(
                                      hour.toString().padLeft(2, '0'),
                                      style: DesignTokens.bodyStyle.copyWith(
                                        color: DesignTokens.textPrimary,
                                        fontSize: 18,
                                      ),
                                    ),
                                  );
                                },
                                childCount: 12,
                              ),
                            ),
                          ),

                          // Separator
                          const Text(
                            ':',
                            style: TextStyle(
                              color: DesignTokens.textPrimary,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),

                          // Minutes
                          Expanded(
                            child: ListWheelScrollView.useDelegate(
                              controller: _minuteController,
                              itemExtent: 40,
                              physics: const FixedExtentScrollPhysics(),
                              onSelectedItemChanged: (index) {
                                setState(() {
                                  _selectedMinute = index;
                                });
                              },
                              childDelegate: ListWheelChildBuilderDelegate(
                                builder: (context, index) {
                                  if (index < 0 || index >= 60) return null;
                                  return Center(
                                    child: Text(
                                      index.toString().padLeft(2, '0'),
                                      style: DesignTokens.bodyStyle.copyWith(
                                        color: DesignTokens.textPrimary,
                                        fontSize: 18,
                                      ),
                                    ),
                                  );
                                },
                                childCount: 60,
                              ),
                            ),
                          ),

                          const SizedBox(width: DesignTokens.spacingSm),

                          // AM/PM
                          Expanded(
                            child: ListWheelScrollView.useDelegate(
                              controller: _periodController,
                              itemExtent: 40,
                              physics: const FixedExtentScrollPhysics(),
                              onSelectedItemChanged: (index) {
                                setState(() {
                                  _selectedPeriod = index;
                                });
                              },
                              childDelegate: ListWheelChildBuilderDelegate(
                                builder: (context, index) {
                                  if (index < 0 || index >= 2) return null;
                                  return Center(
                                    child: Text(
                                      index == 0 ? 'AM' : 'PM',
                                      style: DesignTokens.bodyStyle.copyWith(
                                        color: DesignTokens.textPrimary,
                                        fontSize: 18,
                                      ),
                                    ),
                                  );
                                },
                                childCount: 2,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: DesignTokens.spacingLg),

                    // Confirm Button
                    GestureDetector(
                      onTap: _confirmTime,
                      child: Container(
                        width: 120,
                        height: 40,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: DesignTokens.primaryButtonBackground,
                          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                        ),
                        child: const Text('Confirm', style: DesignTokens.primaryButtonTextStyle),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// Bottom Navigation Bar (same as calendar screen)
class _BottomNavBar extends StatelessWidget {
  final int selectedIndex;

  const _BottomNavBar({required this.selectedIndex});

  @override
  Widget build(BuildContext context) {
    return BottomAppBar(
      color: DesignTokens.backgroundApp,
      elevation: 0,
      child: SizedBox(
        height: 70,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildNavItem(index: 0, iconPath: 'assets/icons/home_chat.svg', label: 'Home'),
            _buildNavItem(index: 1, iconPath: 'assets/icons/calendar_navbar.svg', label: 'Calendar'),
            _buildCentralNavItem(index: 2, iconPath: 'assets/icons/darvisnavbar_icon.svg'),
            _buildNavItem(index: 3, iconPath: 'assets/icons/inbox_icon.svg', label: 'Inbox'),
            _buildNavItem(index: 4, iconPath: 'assets/icons/profile_icon.svg', label: 'Profile'),
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem({required int index, required String iconPath, required String label}) {
    final bool isActive = selectedIndex == index;
    final Color color = isActive ? DesignTokens.primaryInteractiveBlue : DesignTokens.navigationInactive;

    return Expanded(
      child: InkWell(
        onTap: () {
          // Handle navigation
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(iconPath, colorFilter: ColorFilter.mode(color, BlendMode.srcIn), height: 24),
            const SizedBox(height: DesignTokens.spacingXs),
            Text(label, style: DesignTokens.navBarLabelStyle.copyWith(color: color)),
          ],
        ),
      ),
    );
  }

  Widget _buildCentralNavItem({required int index, required String iconPath}) {
    final bool isActive = selectedIndex == index;
    return Expanded(
      child: InkWell(
        onTap: () {
          // Handle navigation
        },
        child: ShaderMask(
          shaderCallback: (bounds) {
            return (isActive ? DesignTokens.backgroundGlowBlueGradient : DesignTokens.appNameHeaderGradient)
                .createShader(bounds);
          },
          child: Container(
            height: 60,
            width: 60,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
            ),
            child: Center(
              child: SvgPicture.asset(iconPath, height: 32),
            ),
          ),
        ),
      ),
    );
  }
}
