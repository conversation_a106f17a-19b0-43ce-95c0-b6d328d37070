import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:darvis_app/utils/design_tokens.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

class AddContactScreen extends StatefulWidget {
  const AddContactScreen({super.key});

  @override
  State<AddContactScreen> createState() => _AddContactScreenState();
}

class _AddContactScreenState extends State<AddContactScreen> with TickerProviderStateMixin {
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _locationController = TextEditingController();
  final _notesController = TextEditingController();
  final _socialUsernameController = TextEditingController();
  
  String? _selectedImage;
  String _selectedSocialPlatform = 'Twitter';
  bool _isLoading = false;
  bool _isLinkButtonLoading = false;
  bool _isLinkButtonSuccess = false;
  bool _isSearchLoading = false;
  bool _showSocialDropdown = false;
  
  final List<String> _socialPlatforms = ['Twitter', 'Instagram', 'LinkedIn', 'WhatsApp'];
  
  late AnimationController _loadingController;
  late AnimationController _successController;
  
  @override
  void initState() {
    super.initState();
    _loadingController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _successController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _locationController.dispose();
    _notesController.dispose();
    _socialUsernameController.dispose();
    _loadingController.dispose();
    _successController.dispose();
    super.dispose();
  }

  void _openCamera() async {
    final ImagePicker picker = ImagePicker();
    try {
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 800,
        maxHeight: 800,
      );

      if (image != null) {
        setState(() {
          _selectedImage = image.path;
        });
      }
    } catch (e) {
      // Handle camera permission or other errors
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Unable to access camera. Please check permissions.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _clearForm() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: DesignTokens.backgroundCard,
        title: const Text('Clear All Fields?', style: DesignTokens.cardTitleStyle),
        content: const Text(
          'This will remove all entered information.',
          style: DesignTokens.bodyStyle,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel', style: DesignTokens.linkTextStyle),
          ),
          TextButton(
            onPressed: () {
              _nameController.clear();
              _phoneController.clear();
              _locationController.clear();
              _notesController.clear();
              _socialUsernameController.clear();
              setState(() {
                _selectedImage = null;
                _selectedSocialPlatform = 'Twitter';
                _isLinkButtonSuccess = false;
              });
              Navigator.of(context).pop();
            },
            child: const Text('Clear All', style: DesignTokens.linkTextStyle),
          ),
        ],
      ),
    );
  }

  Future<void> _generateContactLink() async {
    if (_phoneController.text.isEmpty) return;
    
    setState(() {
      _isLinkButtonLoading = true;
    });
    
    _loadingController.repeat();
    
    // Mock VCF generation process
    await Future.delayed(const Duration(seconds: 2));
    
    _loadingController.stop();
    
    setState(() {
      _isLinkButtonLoading = false;
      _isLinkButtonSuccess = true;
    });
    
    _successController.forward();
  }

  Future<void> _searchSocialMedia() async {
    if (_socialUsernameController.text.isEmpty) return;
    
    setState(() {
      _isSearchLoading = true;
    });
    
    // Mock social media search
    await Future.delayed(const Duration(milliseconds: 800));
    
    final username = _socialUsernameController.text;
    String url;
    
    switch (_selectedSocialPlatform) {
      case 'Twitter':
        url = 'https://twitter.com/$username';
        break;
      case 'Instagram':
        url = 'https://instagram.com/$username';
        break;
      case 'LinkedIn':
        url = 'https://linkedin.com/in/$username';
        break;
      case 'WhatsApp':
        url = 'https://wa.me/$username';
        break;
      default:
        url = 'https://google.com/search?q=$username';
    }
    
    try {
      await launchUrl(Uri.parse(url));
    } catch (e) {
      // Handle error
    }
    
    setState(() {
      _isSearchLoading = false;
    });
  }

  Future<void> _saveContact() async {
    // Validate required fields
    if (_nameController.text.isEmpty || _phoneController.text.isEmpty) {
      // Show validation errors
      return;
    }
    
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: DesignTokens.backgroundCard,
        title: const Text('Save Contact?', style: DesignTokens.cardTitleStyle),
        content: Text(
          'Save ${_nameController.text} to your contacts?',
          style: DesignTokens.bodyStyle,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel', style: DesignTokens.linkTextStyle),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Save', style: DesignTokens.linkTextStyle),
          ),
        ],
      ),
    );
    
    if (confirmed != true) return;
    
    setState(() {
      _isLoading = true;
    });
    
    _loadingController.repeat();
    
    // Mock save process
    await Future.delayed(const Duration(seconds: 2));
    
    _loadingController.stop();
    
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      resizeToAvoidBottomInset: true, // Allow keyboard to push content up
      body: SafeArea(
        child: Column(
          children: [
            _Header(onBackTap: () => Navigator.of(context).pop()),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(
                  horizontal: DesignTokens.spacingMd,
                  vertical: DesignTokens.spacingSm,
                ),
                child: Column(
                  children: [
                    const SizedBox(height: DesignTokens.spacingSm),

                    // Image Upload Section
                    _ImageUploadSection(
                      selectedImage: _selectedImage,
                      onTap: _openCamera,
                    ),

                    const SizedBox(height: DesignTokens.spacingMd),

                    // Form Fields
                    _FormField(
                      label: "What's your Name?",
                      controller: _nameController,
                      isRequired: true,
                    ),
                    const SizedBox(height: DesignTokens.spacingXs),

                    _PhoneField(
                      controller: _phoneController,
                      onLinkTap: _generateContactLink,
                      isLinkLoading: _isLinkButtonLoading,
                      isLinkSuccess: _isLinkButtonSuccess,
                      loadingController: _loadingController,
                    ),
                    const SizedBox(height: DesignTokens.spacingXs),

                    _FormField(
                      label: "Where did our paths cross today",
                      controller: _locationController,
                    ),
                    const SizedBox(height: DesignTokens.spacingXs),

                    _SocialMediaField(
                      controller: _socialUsernameController,
                      selectedPlatform: _selectedSocialPlatform,
                      platforms: _socialPlatforms,
                      showDropdown: _showSocialDropdown,
                      isSearchLoading: _isSearchLoading,
                      onPlatformChanged: (platform) {
                        setState(() {
                          _selectedSocialPlatform = platform;
                          _showSocialDropdown = false;
                        });
                      },
                      onDropdownToggle: () {
                        setState(() {
                          _showSocialDropdown = !_showSocialDropdown;
                        });
                      },
                      onSearchTap: _searchSocialMedia,
                    ),
                    const SizedBox(height: DesignTokens.spacingXs),

                    _ExpandableFormField(
                      label: "What should I remember you by?",
                      controller: _notesController,
                    ),

                    const SizedBox(height: DesignTokens.spacingLg),

                    // Action Buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _ActionButton(
                          text: 'Clear All',
                          onTap: _clearForm,
                          isSecondary: true,
                        ),
                        _ActionButton(
                          text: 'Save Contact',
                          onTap: _saveContact,
                          isLoading: _isLoading,
                          loadingController: _loadingController,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const _BottomNavBar(selectedIndex: 1),
          ],
        ),
      ),
    );
  }
}

// Header Component
class _Header extends StatelessWidget {
  final VoidCallback onBackTap;

  const _Header({required this.onBackTap});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: false,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingMd,
          vertical: DesignTokens.spacingSm,
        ),
        child: Column(
          children: [
            const SizedBox(height: DesignTokens.spacingSm),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: onBackTap,
                  child: const Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const _GradientTitle(text: 'Add Contact'),
                const SizedBox(width: 24), // Balance the back button
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Gradient Title Component
class _GradientTitle extends StatelessWidget {
  final String text;

  const _GradientTitle({required this.text});

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      shaderCallback: (bounds) => DesignTokens.appNameHeaderGradient.createShader(bounds),
      child: Text(
        text,
        style: DesignTokens.appNameStyle.copyWith(
          color: Colors.white,
          fontSize: 35,
        ),
      ),
    );
  }
}

// Image Upload Section
class _ImageUploadSection extends StatelessWidget {
  final String? selectedImage;
  final VoidCallback onTap;

  const _ImageUploadSection({
    required this.selectedImage,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: selectedImage != null ? Colors.transparent : DesignTokens.formFieldBackground,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
          border: selectedImage == null ? Border.all(
            color: DesignTokens.textMuted.withValues(alpha: 0.3),
            width: 1,
          ) : null,
        ),
        child: selectedImage != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              child: Image.file(
                File(selectedImage!),
                fit: BoxFit.cover,
              ),
            )
          : Center(
              child: SvgPicture.asset(
                'assets/icons/add_event.svg',
                width: 32,
                height: 32,
                colorFilter: const ColorFilter.mode(
                  DesignTokens.textMuted,
                  BlendMode.srcIn,
                ),
              ),
            ),
      ),
    );
  }
}

// Form Field Component
class _FormField extends StatelessWidget {
  final String label;
  final TextEditingController controller;
  final bool isRequired;

  const _FormField({
    required this.label,
    required this.controller,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: DesignTokens.formLabelStyle),
        const SizedBox(height: DesignTokens.spacingXs),
        TextField(
          controller: controller,
          style: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
          decoration: InputDecoration(
            filled: true,
            fillColor: DesignTokens.formFieldBackground,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              borderSide: BorderSide.none,
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingMd,
              vertical: DesignTokens.spacingSm,
            ),
          ),
        ),
      ],
    );
  }
}

// Expandable Form Field Component
class _ExpandableFormField extends StatelessWidget {
  final String label;
  final TextEditingController controller;

  const _ExpandableFormField({
    required this.label,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: DesignTokens.formLabelStyle),
        const SizedBox(height: DesignTokens.spacingXs),
        TextField(
          controller: controller,
          minLines: 1,
          maxLines: 4,
          style: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
          decoration: InputDecoration(
            filled: true,
            fillColor: DesignTokens.formFieldBackground,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              borderSide: BorderSide.none,
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingMd,
              vertical: DesignTokens.spacingSm,
            ),
          ),
        ),
      ],
    );
  }
}

// Phone Field with Contact Link Button
class _PhoneField extends StatelessWidget {
  final TextEditingController controller;
  final VoidCallback onLinkTap;
  final bool isLinkLoading;
  final bool isLinkSuccess;
  final AnimationController loadingController;

  const _PhoneField({
    required this.controller,
    required this.onLinkTap,
    required this.isLinkLoading,
    required this.isLinkSuccess,
    required this.loadingController,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Digits Only', style: DesignTokens.formLabelStyle),
        const SizedBox(height: DesignTokens.spacingXs),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: controller,
                keyboardType: TextInputType.phone,
                style: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
                decoration: InputDecoration(
                  filled: true,
                  fillColor: DesignTokens.formFieldBackground,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                    borderSide: BorderSide.none,
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: DesignTokens.spacingMd,
                    vertical: DesignTokens.spacingSm,
                  ),
                ),
              ),
            ),
            const SizedBox(width: DesignTokens.spacingSm),
            GestureDetector(
              onTap: isLinkLoading ? null : onLinkTap,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: isLinkSuccess
                    ? DesignTokens.primaryInteractiveBlue
                    : DesignTokens.primaryAccentBlue,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Center(
                  child: isLinkLoading
                    ? RotationTransition(
                        turns: loadingController,
                        child: const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(DesignTokens.iconPrimary),
                          ),
                        ),
                      )
                    : Icon(
                        isLinkSuccess ? Icons.check : Icons.link,
                        color: DesignTokens.iconPrimary,
                        size: 20,
                      ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

// Social Media Field with Dropdown and Search
class _SocialMediaField extends StatelessWidget {
  final TextEditingController controller;
  final String selectedPlatform;
  final List<String> platforms;
  final bool showDropdown;
  final bool isSearchLoading;
  final Function(String) onPlatformChanged;
  final VoidCallback onDropdownToggle;
  final VoidCallback onSearchTap;

  const _SocialMediaField({
    required this.controller,
    required this.selectedPlatform,
    required this.platforms,
    required this.showDropdown,
    required this.isSearchLoading,
    required this.onPlatformChanged,
    required this.onDropdownToggle,
    required this.onSearchTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Social Media', style: DesignTokens.formLabelStyle),
        const SizedBox(height: DesignTokens.spacingSm),

        // Platform Selector
        GestureDetector(
          onTap: onDropdownToggle,
          child: Container(
            width: 150, // Fixed width instead of full width
            padding: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingSm,
              vertical: DesignTokens.spacingSm,
            ),
            decoration: BoxDecoration(
              color: DesignTokens.formFieldBackground,
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  selectedPlatform,
                  style: DesignTokens.bodyStyle.copyWith(
                    color: DesignTokens.textPrimary,
                    fontSize: 14,
                  ),
                ),
                Icon(
                  showDropdown ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                  color: DesignTokens.textMuted,
                  size: 18,
                ),
              ],
            ),
          ),
        ),

        // Dropdown Options
        if (showDropdown)
          Container(
            width: 150, // Match the selector width
            margin: const EdgeInsets.only(top: DesignTokens.spacingXs),
            decoration: BoxDecoration(
              color: DesignTokens.formFieldBackground,
              borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
            ),
            child: Column(
              children: platforms.map((platform) {
                return GestureDetector(
                  onTap: () => onPlatformChanged(platform),
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      horizontal: DesignTokens.spacingSm,
                      vertical: DesignTokens.spacingXs,
                    ),
                    child: Text(
                      platform,
                      style: DesignTokens.bodyStyle.copyWith(
                        color: platform == selectedPlatform
                          ? DesignTokens.primaryInteractiveBlue
                          : DesignTokens.textPrimary,
                        fontSize: 14,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),

        const SizedBox(height: DesignTokens.spacingSm),

        // Username Input with Search Button
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: controller,
                style: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary),
                decoration: InputDecoration(
                  filled: true,
                  fillColor: DesignTokens.formFieldBackground,
                  hintText: 'Username',
                  hintStyle: DesignTokens.bodyStyle.copyWith(color: DesignTokens.textMuted),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
                    borderSide: BorderSide.none,
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: DesignTokens.spacingMd,
                    vertical: DesignTokens.spacingMd,
                  ),
                ),
              ),
            ),
            const SizedBox(width: DesignTokens.spacingSm),
            GestureDetector(
              onTap: isSearchLoading ? null : onSearchTap,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: DesignTokens.primaryAccentBlue,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Center(
                  child: isSearchLoading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(DesignTokens.iconPrimary),
                        ),
                      )
                    : SvgPicture.asset(
                        'assets/icons/search_contact.svg',
                        width: 20,
                        height: 20,
                        colorFilter: const ColorFilter.mode(
                          DesignTokens.iconPrimary,
                          BlendMode.srcIn,
                        ),
                      ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

// Action Button Component
class _ActionButton extends StatelessWidget {
  final String text;
  final VoidCallback onTap;
  final bool isLoading;
  final bool isSecondary;
  final AnimationController? loadingController;

  const _ActionButton({
    required this.text,
    required this.onTap,
    this.isLoading = false,
    this.isSecondary = false,
    this.loadingController,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: isLoading ? null : onTap,
      child: Container(
        width: 140,
        height: 45,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: isLoading
            ? Colors.transparent
            : isSecondary
              ? DesignTokens.formFieldBackground
              : DesignTokens.primaryButtonBackground,
          gradient: isLoading ? DesignTokens.appNameHeaderGradient : null,
          borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
        ),
        child: isLoading && loadingController != null
          ? RotationTransition(
              turns: loadingController!,
              child: const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          : Text(
              text,
              style: isSecondary
                ? DesignTokens.bodyStyle.copyWith(color: DesignTokens.textPrimary)
                : DesignTokens.primaryButtonTextStyle,
            ),
      ),
    );
  }
}



// Bottom Navigation Bar
class _BottomNavBar extends StatelessWidget {
  final int selectedIndex;

  const _BottomNavBar({required this.selectedIndex});

  @override
  Widget build(BuildContext context) {
    return BottomAppBar(
      color: DesignTokens.backgroundApp,
      elevation: 0,
      child: SizedBox(
        height: 70,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildNavItem(index: 0, iconPath: 'assets/icons/home_chat.svg', label: 'Home'),
            _buildNavItem(index: 1, iconPath: 'assets/icons/calendar_navbar.svg', label: 'Calendar'),
            _buildCentralNavItem(index: 2, iconPath: 'assets/icons/darvisnavbar_icon.svg'),
            _buildNavItem(index: 3, iconPath: 'assets/icons/inbox_icon.svg', label: 'Inbox'),
            _buildNavItem(index: 4, iconPath: 'assets/icons/profile_icon.svg', label: 'Profile'),
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem({required int index, required String iconPath, required String label}) {
    final bool isActive = selectedIndex == index;
    final Color color = isActive ? DesignTokens.primaryInteractiveBlue : DesignTokens.navigationInactive;

    return Expanded(
      child: InkWell(
        onTap: () {
          // Handle navigation
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(iconPath, colorFilter: ColorFilter.mode(color, BlendMode.srcIn), height: 24),
            const SizedBox(height: DesignTokens.spacingXs),
            Text(label, style: DesignTokens.navBarLabelStyle.copyWith(color: color)),
          ],
        ),
      ),
    );
  }

  Widget _buildCentralNavItem({required int index, required String iconPath}) {
    final bool isActive = selectedIndex == index;
    return Expanded(
      child: InkWell(
        onTap: () {
          // Handle navigation
        },
        child: ShaderMask(
          shaderCallback: (bounds) {
            return (isActive ? DesignTokens.backgroundGlowBlueGradient : DesignTokens.appNameHeaderGradient)
                .createShader(bounds);
          },
          child: Container(
            height: 60,
            width: 60,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
            ),
            child: Center(
              child: SvgPicture.asset(iconPath, height: 32),
            ),
          ),
        ),
      ),
    );
  }
}
