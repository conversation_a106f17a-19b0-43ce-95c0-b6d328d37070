import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:darvis_app/utils/design_tokens.dart';
import 'note_view_screen.dart';

// Note data model
class Note {
  final String id;
  String title;
  String content;
  List<String> tags;
  bool isPinned;
  DateTime createdAt;
  DateTime updatedAt;
  Color backgroundColor;

  Note({
    required this.id,
    required this.title,
    required this.content,
    this.tags = const [],
    this.isPinned = false,
    required this.createdAt,
    required this.updatedAt,
    required this.backgroundColor,
  });
}

// Selection mode enum
enum SelectionMode { none, active }

/// NotesScreen: Main productivity notes interface
class NotesScreen extends StatefulWidget {
  const NotesScreen({super.key});

  @override
  State<NotesScreen> createState() => _NotesScreenState();
}

class _NotesScreenState extends State<NotesScreen>
    with TickerProviderStateMixin {
  
  final TextEditingController _searchTextController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  SelectionMode _selectionMode = SelectionMode.none;
  Set<String> _selectedNotes = {};
  bool _isSearchActive = false;
  bool _isTagSidebarOpen = false;
  String _activeTagFilter = '';

  late AnimationController _searchAnimationController;
  late AnimationController _sidebarController;
  late AnimationController _selectionController;
  late Animation<double> _searchAnimation;
  late Animation<double> _sidebarAnimation;
  late Animation<double> _selectionAnimation;

  // Mock data for testing
  List<Note> _allNotes = [];
  List<String> _allTags = [];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _generateMockData();
    _searchTextController.addListener(_onSearchChanged);
    _searchFocusNode.addListener(_onSearchFocusChanged);
  }

  void _initializeAnimations() {
    _searchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _sidebarController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _selectionController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _searchAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _searchAnimationController, curve: Curves.easeOut),
    );
    _sidebarAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _sidebarController, curve: Curves.easeOut),
    );
    _selectionAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _selectionController, curve: Curves.easeOut),
    );
  }

  void _generateMockData() {
    final now = DateTime.now();
    _allNotes = [
      Note(
        id: '1',
        title: 'Meeting Notes - Q4 Planning',
        content: 'Discussed quarterly goals, budget allocation, and team restructuring. Key decisions: hire 2 new developers, increase marketing budget by 20%, launch new product line in March.',
        tags: ['work', 'meetings', 'planning'],
        isPinned: true,
        createdAt: now.subtract(const Duration(days: 1)),
        updatedAt: now.subtract(const Duration(hours: 2)),
        backgroundColor: DesignTokens.primaryAccentBlue,
      ),
      Note(
        id: '2',
        title: 'Daily Journal - Monday',
        content: 'Started the week with good energy. Completed the presentation for tomorrow\'s client meeting. Feeling optimistic about the new project proposal.',
        tags: ['personal', 'journal', 'mood'],
        isPinned: false,
        createdAt: now.subtract(const Duration(days: 2)),
        updatedAt: now.subtract(const Duration(days: 1)),
        backgroundColor: DesignTokens.backgroundCard,
      ),
      Note(
        id: '3',
        title: 'App Ideas',
        content: 'Voice-controlled note taking, AI-powered task prioritization, collaborative workspace for remote teams, meditation app with biometric feedback.',
        tags: ['ideas', 'apps', 'innovation'],
        isPinned: true,
        createdAt: now.subtract(const Duration(days: 3)),
        updatedAt: now.subtract(const Duration(hours: 5)),
        backgroundColor: DesignTokens.backgroundCard,
      ),
      Note(
        id: '4',
        title: 'Grocery List',
        content: 'Milk, eggs, bread, chicken breast, spinach, tomatoes, olive oil, pasta, cheese, yogurt, bananas, apples.',
        tags: ['personal', 'shopping'],
        isPinned: false,
        createdAt: now.subtract(const Duration(days: 4)),
        updatedAt: now.subtract(const Duration(days: 3)),
        backgroundColor: DesignTokens.primaryAccentBlue,
      ),
      Note(
        id: '5',
        title: 'Book Recommendations',
        content: 'Atomic Habits by James Clear, The Psychology of Money by Morgan Housel, Thinking Fast and Slow by Daniel Kahneman, Deep Work by Cal Newport.',
        tags: ['books', 'learning', 'personal'],
        isPinned: false,
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now.subtract(const Duration(days: 4)),
        backgroundColor: DesignTokens.backgroundCard,
      ),
      Note(
        id: '6',
        title: 'Workout Plan',
        content: 'Monday: Chest & Triceps, Tuesday: Back & Biceps, Wednesday: Legs, Thursday: Shoulders, Friday: Cardio, Weekend: Rest or light yoga.',
        tags: ['fitness', 'health', 'routine'],
        isPinned: false,
        createdAt: now.subtract(const Duration(days: 6)),
        updatedAt: now.subtract(const Duration(days: 5)),
        backgroundColor: DesignTokens.primaryAccentBlue,
      ),
    ];

    // Extract all unique tags
    _allTags = _allNotes
        .expand((note) => note.tags)
        .toSet()
        .toList()
        ..sort();
  }

  @override
  void dispose() {
    _searchAnimationController.dispose();
    _sidebarController.dispose();
    _selectionController.dispose();
    _searchTextController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      // Trigger rebuild for real-time search
    });
  }

  void _onSearchFocusChanged() {
    setState(() {
      _isSearchActive = _searchFocusNode.hasFocus;
    });

    if (_isSearchActive) {
      _searchAnimationController.forward();
    } else {
      _searchAnimationController.reverse();
    }
  }

  void _clearSearchFocus() {
    _searchFocusNode.unfocus();
    setState(() {
      _isSearchActive = false;
    });
    _searchAnimationController.reverse();
  }

  void _toggleTagSidebar() {
    setState(() {
      _isTagSidebarOpen = !_isTagSidebarOpen;
    });
    
    if (_isTagSidebarOpen) {
      _sidebarController.forward();
    } else {
      _sidebarController.reverse();
    }
  }

  void _onNoteTap(Note note) {
    if (_selectionMode == SelectionMode.active) {
      _toggleNoteSelection(note.id);
    } else {
      _openNoteView(note);
    }
  }

  void _onNoteLongPress(Note note) {
    HapticFeedback.mediumImpact();
    setState(() {
      _selectionMode = SelectionMode.active;
      _selectedNotes.add(note.id);
    });
    _selectionController.forward();
  }

  void _toggleNoteSelection(String noteId) {
    setState(() {
      if (_selectedNotes.contains(noteId)) {
        _selectedNotes.remove(noteId);
        if (_selectedNotes.isEmpty) {
          _exitSelectionMode();
        }
      } else {
        _selectedNotes.add(noteId);
      }
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _selectionMode = SelectionMode.none;
      _selectedNotes.clear();
    });
    _selectionController.reverse();
  }

  void _openNoteView(Note note) {
    _clearSearchFocus();
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => NoteViewScreen(note: note),
      ),
    );
  }

  void _createNewNote() {
    // Create new note logic
    print('Create new note');
  }

  void _showOptionsMenu() {
    // Show options menu
    print('Show options menu');
  }

  void _filterByTag(String tag) {
    setState(() {
      _activeTagFilter = tag;
      _isTagSidebarOpen = false;
    });
    _sidebarController.reverse();
  }

  void _clearTagFilter() {
    setState(() {
      _activeTagFilter = '';
      _isTagSidebarOpen = false;
    });
    _sidebarController.reverse();
  }

  List<Note> get _filteredNotes {
    var notes = List<Note>.from(_allNotes);
    
    // Apply tag filter
    if (_activeTagFilter.isNotEmpty) {
      notes = notes.where((note) => note.tags.contains(_activeTagFilter)).toList();
    }
    
    // Apply search filter
    if (_searchTextController.text.isNotEmpty) {
      final searchTerm = _searchTextController.text.toLowerCase();
      notes = notes.where((note) {
        return note.title.toLowerCase().contains(searchTerm) ||
               note.content.toLowerCase().contains(searchTerm) ||
               note.tags.any((tag) => tag.toLowerCase().contains(searchTerm));
      }).toList();
    }
    
    // Sort: pinned notes first, then by updated date
    notes.sort((a, b) {
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;
      return b.updatedAt.compareTo(a.updatedAt);
    });
    
    return notes;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignTokens.backgroundApp,
      body: GestureDetector(
        onTap: () {
          // Dismiss keyboard and clear search focus when tapping outside
          if (_isSearchActive) {
            _clearSearchFocus();
          }
          // Exit selection mode when tapping outside
          if (_selectionMode == SelectionMode.active) {
            _exitSelectionMode();
          }
        },
        child: Stack(
          children: [
            // Main content
            SafeArea(
              child: Column(
                children: [
                  // Header
                  _Header(
                    onBackTap: () => Navigator.of(context).pop(),
                    onMenuTap: _showOptionsMenu,
                  ),

                  // Search bar
                  _SearchBar(
                    controller: _searchTextController,
                    focusNode: _searchFocusNode,
                    animation: _searchAnimation,
                    isActive: _isSearchActive,
                  ),

                  // Notes grid
                  Expanded(
                    child: _NotesGrid(
                      notes: _filteredNotes,
                      selectionMode: _selectionMode,
                      selectedNotes: _selectedNotes,
                      onNoteTap: _onNoteTap,
                      onNoteLongPress: _onNoteLongPress,
                      activeTagFilter: _activeTagFilter,
                      searchTerm: _searchTextController.text,
                    ),
                  ),
                ],
              ),
            ),
          
          // Floating Action Button
          Positioned(
            bottom: 16 + MediaQuery.of(context).padding.bottom,
            right: 16,
            child: _FloatingActionButton(onTap: _createNewNote),
          ),
          
          // Selection mode bottom bar
          if (_selectionMode == SelectionMode.active)
            _SelectionBottomBar(
              selectedCount: _selectedNotes.length,
              animation: _selectionAnimation,
              onExitSelection: _exitSelectionMode,
            ),
          
          // Tag sidebar
          if (_isTagSidebarOpen)
            _TagSidebar(
              tags: _allTags,
              animation: _sidebarAnimation,
              onTagTap: _filterByTag,
              onClearFilter: _clearTagFilter,
              onDismiss: _toggleTagSidebar,
              activeFilter: _activeTagFilter,
            ),
          
          // Sidebar peek hint
          if (!_isTagSidebarOpen)
            _SidebarPeekHint(onSwipe: _toggleTagSidebar),
        ],
      ),
      ),
    );
  }
}

// Header Component
class _Header extends StatelessWidget {
  final VoidCallback onBackTap;
  final VoidCallback onMenuTap;

  const _Header({
    required this.onBackTap,
    required this.onMenuTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingLg,
        vertical: DesignTokens.spacingLg,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: onBackTap,
            child: Container(
              padding: const EdgeInsets.all(DesignTokens.spacingSm),
              decoration: BoxDecoration(
                color: DesignTokens.backgroundCard.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              ),
              child: const Icon(
                Icons.arrow_back,
                color: DesignTokens.iconPrimary,
                size: 24,
              ),
            ),
          ),
          ShaderMask(
            shaderCallback: (bounds) => DesignTokens.appNameHeaderGradient.createShader(bounds),
            child: Text(
              'Notes',
              style: DesignTokens.therapySessionTitleStyle.copyWith(
                color: Colors.white,
                fontSize: 32,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
          GestureDetector(
            onTap: onMenuTap,
            child: Container(
              padding: const EdgeInsets.all(DesignTokens.spacingSm),
              decoration: BoxDecoration(
                color: DesignTokens.backgroundCard.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(DesignTokens.borderRadiusMd),
              ),
              child: const Icon(
                Icons.more_vert,
                color: DesignTokens.iconPrimary,
                size: 24,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Search Bar Component
class _SearchBar extends StatelessWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final Animation<double> animation;
  final bool isActive;

  const _SearchBar({
    required this.controller,
    required this.focusNode,
    required this.animation,
    required this.isActive,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AnimatedBuilder(
            animation: animation,
            builder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  color: Color.lerp(
                    DesignTokens.backgroundCard,
                    DesignTokens.backgroundTextInput,
                    animation.value,
                  ),
                  borderRadius: BorderRadius.circular(DesignTokens.borderRadiusLg),
                ),
                child: TextField(
                  controller: controller,
                  focusNode: focusNode,
                  style: DesignTokens.bodyStyle.copyWith(
                    color: Color.lerp(
                      DesignTokens.textSecondary,
                      DesignTokens.textOnLightBackground,
                      animation.value,
                    ),
                  ),
                  decoration: InputDecoration(
                    hintText: isActive ? 'Search notes, tags, or content' : 'Search',
                    hintStyle: DesignTokens.bodyStyle.copyWith(
                      color: Color.lerp(
                        DesignTokens.textSecondary,
                        DesignTokens.textOnLightBackground.withValues(alpha: 0.6),
                        animation.value,
                      ),
                    ),
                    prefixIcon: Icon(
                      Icons.search,
                      color: Color.lerp(
                        DesignTokens.iconSecondary,
                        DesignTokens.textOnLightBackground,
                        animation.value,
                      ),
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: DesignTokens.spacingMd,
                      vertical: DesignTokens.spacingMd,
                    ),
                  ),
                ),
              );
            },
          ),

          // Search hint when active
          if (isActive)
            Padding(
              padding: const EdgeInsets.only(
                left: DesignTokens.spacingMd,
                top: DesignTokens.spacingSm,
              ),
              child: Text(
                'Swipe right to browse all tags',
                style: DesignTokens.bodyStyle.copyWith(
                  fontSize: 12,
                  color: DesignTokens.textSecondary.withValues(alpha: 0.7),
                ),
              ),
            ),

          const SizedBox(height: DesignTokens.spacingMd),
        ],
      ),
    );
  }
}

// Notes Grid Component
class _NotesGrid extends StatelessWidget {
  final List<Note> notes;
  final SelectionMode selectionMode;
  final Set<String> selectedNotes;
  final Function(Note) onNoteTap;
  final Function(Note) onNoteLongPress;
  final String activeTagFilter;
  final String searchTerm;

  const _NotesGrid({
    required this.notes,
    required this.selectionMode,
    required this.selectedNotes,
    required this.onNoteTap,
    required this.onNoteLongPress,
    required this.activeTagFilter,
    required this.searchTerm,
  });

  @override
  Widget build(BuildContext context) {
    if (notes.isEmpty) {
      return _EmptyState(
        activeTagFilter: activeTagFilter,
        searchTerm: searchTerm,
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingMd),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: DesignTokens.spacingMd,
          mainAxisSpacing: DesignTokens.spacingMd,
          childAspectRatio: 0.8,
        ),
        itemCount: notes.length,
        itemBuilder: (context, index) {
          final note = notes[index];
          final isSelected = selectedNotes.contains(note.id);
          final isLeftColumn = index % 2 == 0;

          return _NoteCard(
            note: note,
            isSelected: isSelected,
            isLeftColumn: isLeftColumn,
            selectionMode: selectionMode,
            onTap: () => onNoteTap(note),
            onLongPress: () => onNoteLongPress(note),
            searchTerm: searchTerm,
          );
        },
      ),
    );
  }
}

// Note Card Component
class _NoteCard extends StatelessWidget {
  final Note note;
  final bool isSelected;
  final bool isLeftColumn;
  final SelectionMode selectionMode;
  final VoidCallback onTap;
  final VoidCallback onLongPress;
  final String searchTerm;

  const _NoteCard({
    required this.note,
    required this.isSelected,
    required this.isLeftColumn,
    required this.selectionMode,
    required this.onTap,
    required this.onLongPress,
    required this.searchTerm,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      onLongPress: onLongPress,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: note.backgroundColor,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(isLeftColumn ? 0 : DesignTokens.borderRadiusMd),
            topRight: Radius.circular(isLeftColumn ? DesignTokens.borderRadiusMd : 0),
            bottomLeft: const Radius.circular(DesignTokens.borderRadiusMd),
            bottomRight: const Radius.circular(DesignTokens.borderRadiusMd),
          ),
          border: isSelected
              ? Border.all(color: DesignTokens.primaryInteractiveBlue, width: 2)
              : null,
        ),
        child: Stack(
          children: [
            // Note content
            Padding(
              padding: const EdgeInsets.all(DesignTokens.spacingMd),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Text(
                    note.title,
                    style: DesignTokens.cardTitleStyle.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: DesignTokens.spacingSm),

                  // Content preview
                  Expanded(
                    child: Text(
                      note.content,
                      style: DesignTokens.bodyStyle.copyWith(
                        color: DesignTokens.textSecondary,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // Tags
                  if (note.tags.isNotEmpty) ...[
                    const SizedBox(height: DesignTokens.spacingSm),
                    Wrap(
                      spacing: DesignTokens.spacingXs,
                      runSpacing: DesignTokens.spacingXs,
                      children: note.tags.take(2).map((tag) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: DesignTokens.spacingSm,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: DesignTokens.primaryAccentBlue.withValues(alpha: 0.3),
                            borderRadius: BorderRadius.circular(DesignTokens.borderRadiusSm),
                          ),
                          child: Text(
                            tag,
                            style: DesignTokens.bodyStyle.copyWith(
                              fontSize: 10,
                              color: DesignTokens.textPrimary,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                ],
              ),
            ),

            // Pin indicator
            if (note.isPinned)
              Positioned(
                top: DesignTokens.spacingSm,
                right: DesignTokens.spacingSm,
                child: Icon(
                  Icons.push_pin,
                  size: 16,
                  color: DesignTokens.iconSecondary,
                ),
              ),

            // Selection indicator
            if (selectionMode == SelectionMode.active)
              Positioned(
                top: DesignTokens.spacingSm,
                right: DesignTokens.spacingSm,
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: isSelected
                        ? DesignTokens.primaryInteractiveBlue
                        : Colors.transparent,
                    border: Border.all(
                      color: DesignTokens.primaryInteractiveBlue,
                      width: 2,
                    ),
                    shape: BoxShape.circle,
                  ),
                  child: isSelected
                      ? const Icon(
                          Icons.check,
                          size: 16,
                          color: Colors.white,
                        )
                      : null,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

// Empty State Component
class _EmptyState extends StatelessWidget {
  final String activeTagFilter;
  final String searchTerm;

  const _EmptyState({
    required this.activeTagFilter,
    required this.searchTerm,
  });

  @override
  Widget build(BuildContext context) {
    String title;
    String subtitle;

    if (searchTerm.isNotEmpty) {
      title = 'No notes found for "$searchTerm"';
      subtitle = 'Try searching for tags, titles, or content';
    } else if (activeTagFilter.isNotEmpty) {
      title = 'No notes tagged with "$activeTagFilter"';
      subtitle = 'Create a note with this tag';
    } else {
      title = 'Start capturing your thoughts';
      subtitle = 'Tap + to create your first note';
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacingXl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.note_add,
              size: 64,
              color: DesignTokens.iconSecondary,
            ),
            const SizedBox(height: DesignTokens.spacingLg),
            Text(
              title,
              style: DesignTokens.cardTitleStyle,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: DesignTokens.spacingSm),
            Text(
              subtitle,
              style: DesignTokens.bodyStyle.copyWith(
                color: DesignTokens.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

// Floating Action Button Component
class _FloatingActionButton extends StatelessWidget {
  final VoidCallback onTap;

  const _FloatingActionButton({required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 56,
        height: 56,
        decoration: BoxDecoration(
          color: DesignTokens.primaryInteractiveBlue,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: DesignTokens.primaryInteractiveBlue.withValues(alpha: 0.3),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: const Icon(
          Icons.add,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }
}

// Selection Bottom Bar Component
class _SelectionBottomBar extends StatelessWidget {
  final int selectedCount;
  final Animation<double> animation;
  final VoidCallback onExitSelection;

  const _SelectionBottomBar({
    required this.selectedCount,
    required this.animation,
    required this.onExitSelection,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, (1 - animation.value) * 100),
          child: Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.only(
                left: DesignTokens.spacingLg,
                right: DesignTokens.spacingLg,
                top: DesignTokens.spacingMd,
                bottom: DesignTokens.spacingMd + MediaQuery.of(context).padding.bottom,
              ),
              decoration: BoxDecoration(
                color: DesignTokens.backgroundCard.withValues(alpha: 0.95),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Text(
                    '$selectedCount notes selected',
                    style: DesignTokens.bodyStyle.copyWith(
                      color: DesignTokens.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  _ActionButton(
                    icon: Icons.delete,
                    label: 'Delete',
                    onTap: () => print('Delete selected notes'),
                  ),
                  const SizedBox(width: DesignTokens.spacingMd),
                  _ActionButton(
                    icon: Icons.label,
                    label: 'Tag',
                    onTap: () => print('Add tags to selected notes'),
                  ),
                  const SizedBox(width: DesignTokens.spacingMd),
                  _ActionButton(
                    icon: Icons.push_pin,
                    label: 'Pin',
                    onTap: () => print('Pin selected notes'),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

// Action Button Component
class _ActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onTap;

  const _ActionButton({
    required this.icon,
    required this.label,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: DesignTokens.iconSecondary,
            size: 20,
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: DesignTokens.bodyStyle.copyWith(
              color: DesignTokens.textSecondary,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }
}

// Tag Sidebar Component
class _TagSidebar extends StatelessWidget {
  final List<String> tags;
  final Animation<double> animation;
  final Function(String) onTagTap;
  final VoidCallback onClearFilter;
  final VoidCallback onDismiss;
  final String activeFilter;

  const _TagSidebar({
    required this.tags,
    required this.animation,
    required this.onTagTap,
    required this.onClearFilter,
    required this.onDismiss,
    required this.activeFilter,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return Stack(
          children: [
            // Overlay
            GestureDetector(
              onTap: onDismiss,
              child: Container(
                color: Colors.black.withValues(alpha: 0.5 * animation.value),
              ),
            ),

            // Sidebar
            Transform.translate(
              offset: Offset((-MediaQuery.of(context).size.width * 0.35) * (1 - animation.value), 0),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.35,
                  height: double.infinity,
                  decoration: BoxDecoration(
                    color: DesignTokens.backgroundSideMenu,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 10,
                        offset: const Offset(2, 0),
                      ),
                    ],
                  ),
                  child: SafeArea(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        Padding(
                          padding: const EdgeInsets.all(DesignTokens.spacingLg),
                          child: Text(
                            'Tags',
                            style: DesignTokens.menuSectionTitleStyle,
                          ),
                        ),

                        // All Notes option
                        _TagItem(
                          name: 'All Notes',
                          isActive: activeFilter.isEmpty,
                          onTap: onClearFilter,
                        ),

                        // Tag list
                        Expanded(
                          child: ListView.builder(
                            itemCount: tags.length,
                            itemBuilder: (context, index) {
                              final tag = tags[index];
                              return _TagItem(
                                name: tag,
                                isActive: activeFilter == tag,
                                onTap: () => onTagTap(tag),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

// Tag Item Component
class _TagItem extends StatelessWidget {
  final String name;
  final bool isActive;
  final VoidCallback onTap;

  const _TagItem({
    required this.name,
    required this.isActive,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingLg,
          vertical: DesignTokens.spacingMd,
        ),
        color: isActive
            ? DesignTokens.primaryAccentBlue.withValues(alpha: 0.2)
            : Colors.transparent,
        child: Text(
          name,
          style: DesignTokens.menuItemTextStyle.copyWith(
            color: isActive
                ? DesignTokens.primaryInteractiveBlue
                : DesignTokens.textPrimary,
            fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
      ),
    );
  }
}

// Sidebar Peek Hint Component
class _SidebarPeekHint extends StatelessWidget {
  final VoidCallback onSwipe;

  const _SidebarPeekHint({required this.onSwipe});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 0,
      top: 0,
      bottom: 0,
      child: GestureDetector(
        onHorizontalDragEnd: (details) {
          if (details.primaryVelocity != null && details.primaryVelocity! > 50) {
            onSwipe();
          }
        },
        onHorizontalDragUpdate: (details) {
          // Detect swipe from left edge
          if (details.delta.dx > 2) {
            onSwipe();
          }
        },
        child: SizedBox(
          width: 20, // Wider touch area for better gesture detection
          child: Stack(
            children: [
              // Visible peek edge
              Positioned(
                left: 0,
                top: 0,
                bottom: 0,
                child: Container(
                  width: 3,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: [
                        DesignTokens.backgroundSideMenu,
                        DesignTokens.backgroundSideMenu.withValues(alpha: 0.3),
                        Colors.transparent,
                      ],
                      stops: const [0.0, 0.7, 1.0],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
